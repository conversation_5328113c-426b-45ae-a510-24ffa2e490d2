#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
接口规范同步工具
将 apitest-final.mdc 独有的57个接口规范添加到 apitest-code.mdc 文档中
"""

import re
from typing import List, Dict, Set, <PERSON><PERSON>

def extract_api_interfaces_from_final_file(file_path: str) -> List[Tuple[str, int, str]]:
    """
    从 apitest-final.mdc 文件中提取API接口
    返回: [(接口定义, 行号, 完整行内容), ...]
    """
    interfaces = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 匹配格式：空格+http协议+空格+api接口地址
        pattern = r' (GET|POST|PUT|DELETE|PATCH) (/api/[^\s]+)'
        
        for line_num, line in enumerate(lines, 1):
            match = re.search(pattern, line)
            if match:
                method = match.group(1)
                path = match.group(2)
                interface_key = f"{method} {path}"
                interfaces.append((interface_key, line_num, line.strip()))
                
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
        return []
    except Exception as e:
        print(f"错误：读取文件时发生异常 - {e}")
        return []
    
    return interfaces

def extract_api_interfaces_from_code_file(file_path: str) -> List[Tuple[str, int, str]]:
    """
    从 apitest-code.mdc 文件中提取API接口
    返回: [(接口定义, 行号, 完整行内容), ...]
    """
    interfaces = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 匹配格式：- [ ] **X.Y** 接口名称 `HTTP方法 /api/路径`
        pattern = r'- \[ \] \*\*\d+\.\d+\*\* .* `(GET|POST|PUT|DELETE|PATCH) (/api/[^`]+)`'
        
        for line_num, line in enumerate(lines, 1):
            match = re.search(pattern, line)
            if match:
                method = match.group(1)
                path = match.group(2)
                interface_key = f"{method} {path}"
                interfaces.append((interface_key, line_num, line.strip()))
                
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
        return []
    except Exception as e:
        print(f"错误：读取文件时发生异常 - {e}")
        return []
    
    return interfaces

def find_unique_interfaces(final_interfaces: List[Tuple[str, int, str]], 
                          code_interfaces: List[Tuple[str, int, str]]) -> List[Tuple[str, int, str]]:
    """
    找出 apitest-final.mdc 独有的接口
    """
    # 转换为集合进行差异计算，去除重复
    final_set = {interface[0] for interface in final_interfaces}
    code_set = {interface[0] for interface in code_interfaces}
    
    only_in_final_keys = final_set - code_set
    
    # 获取详细信息，每个接口只保留第一次出现的位置
    final_dict = {}
    for key, line, content in final_interfaces:
        if key not in final_dict:
            final_dict[key] = (line, content)
    
    only_in_final = [(key, final_dict[key][0], final_dict[key][1]) for key in only_in_final_keys]
    
    return sorted(only_in_final)

def convert_final_format_to_code_format(interface_key: str, line_content: str, next_number: str) -> str:
    """
    将 apitest-final.mdc 格式转换为 apitest-code.mdc 格式
    """
    # 从原始内容中提取接口名称
    # 格式：#### 步骤X: Y.Z 接口名称 HTTP方法 /api/路径
    pattern = r'#### 步骤\d+: \d+\.\d+ (.+?) (GET|POST|PUT|DELETE|PATCH) (/api/[^\s]+)'
    match = re.search(pattern, line_content)
    
    if match:
        interface_name = match.group(1)
        method = match.group(2)
        path = match.group(3)
        
        # 转换为 apitest-code.mdc 格式
        # 格式：- [ ] **X.Y** 接口名称 `HTTP方法 /api/路径`
        return f"- [ ] **{next_number}** {interface_name} `{method} {path}`"
    else:
        # 如果无法解析，使用默认格式
        return f"- [ ] **{next_number}** 新增接口 `{interface_key}`"

def get_next_interface_number(code_file_path: str) -> str:
    """
    获取下一个接口编号
    """
    try:
        with open(code_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找所有的接口编号
        pattern = r'- \[ \] \*\*(\d+)\.(\d+)\*\*'
        matches = re.findall(pattern, content)
        
        if matches:
            # 找到最大的编号
            max_major = max(int(match[0]) for match in matches)
            max_minor = max(int(match[1]) for match in matches if int(match[0]) == max_major)
            
            # 返回下一个编号
            return f"{max_major + 1}.1"
        else:
            return "1.1"
            
    except Exception as e:
        print(f"获取接口编号时发生错误: {e}")
        return "999.1"

def add_interfaces_to_code_file(code_file_path: str, unique_interfaces: List[Tuple[str, int, str]]):
    """
    将独有接口添加到 apitest-code.mdc 文件中
    """
    try:
        # 读取原文件内容
        with open(code_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 获取起始编号
        start_number = get_next_interface_number(code_file_path)
        major_num = int(start_number.split('.')[0])
        
        # 准备要添加的内容
        new_content = []
        new_content.append("\n")
        new_content.append("## 从 apitest-final.mdc 同步的新增接口\n")
        new_content.append("\n")
        
        for i, (interface_key, original_line, original_content) in enumerate(unique_interfaces):
            current_number = f"{major_num}.{i + 1}"
            converted_line = convert_final_format_to_code_format(interface_key, original_content, current_number)
            new_content.append(converted_line + "\n")
        
        # 将新内容添加到文件末尾
        lines.extend(new_content)
        
        # 写回文件
        with open(code_file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
            
        print(f"✅ 成功添加 {len(unique_interfaces)} 个接口到 {code_file_path}")
        
    except Exception as e:
        print(f"❌ 添加接口时发生错误: {e}")

def generate_sync_report(unique_interfaces: List[Tuple[str, int, str]]) -> str:
    """
    生成同步报告
    """
    report = []
    report.append("# apitest-final.mdc 独有接口同步报告")
    report.append("")
    report.append(f"## 同步概览")
    report.append(f"- **同步接口数量**: {len(unique_interfaces)}")
    report.append(f"- **目标文档**: apitest-code.mdc")
    report.append(f"- **同步时间**: 2025-07-29")
    report.append("")
    
    report.append("## 同步接口列表")
    report.append("")
    
    # 按HTTP方法分类
    methods = {}
    for interface_key, _, _ in unique_interfaces:
        method = interface_key.split(' ')[0]
        if method not in methods:
            methods[method] = []
        methods[method].append(interface_key)
    
    for method in sorted(methods.keys()):
        report.append(f"### {method} 方法接口 ({len(methods[method])}个)")
        report.append("")
        for i, interface in enumerate(sorted(methods[method]), 1):
            report.append(f"{i}. `{interface}`")
        report.append("")
    
    report.append("## 同步结果")
    report.append("✅ 所有独有接口已成功添加到 apitest-code.mdc 文档中")
    report.append("")
    report.append("## 后续建议")
    report.append("1. **功能验证**: 验证新增接口的功能完整性")
    report.append("2. **编号调整**: 根据需要调整接口编号顺序")
    report.append("3. **分类整理**: 将新增接口按功能模块重新分类")
    report.append("4. **文档审查**: 进行完整的文档审查和校对")
    
    return "\n".join(report)

def main():
    """主函数"""
    final_file = "apitest-final.mdc"
    code_file = "apitest-code.mdc"
    
    print("🔄 开始同步 apitest-final.mdc 独有接口到 apitest-code.mdc...")
    print(f"📄 源文件: {final_file}")
    print(f"📄 目标文件: {code_file}")
    print()
    
    # 提取两个文件中的API接口
    final_interfaces = extract_api_interfaces_from_final_file(final_file)
    code_interfaces = extract_api_interfaces_from_code_file(code_file)
    
    if not final_interfaces:
        print("❌ 未找到源文件中的API接口定义")
        return
    
    if not code_interfaces:
        print("❌ 未找到目标文件中的API接口定义")
        return
    
    # 找出独有接口
    unique_interfaces = find_unique_interfaces(final_interfaces, code_interfaces)
    
    print(f"📊 找到 {len(unique_interfaces)} 个独有接口需要同步")
    
    if not unique_interfaces:
        print("✅ 没有需要同步的接口")
        return
    
    # 添加接口到目标文件
    add_interfaces_to_code_file(code_file, unique_interfaces)
    
    # 生成同步报告
    report = generate_sync_report(unique_interfaces)
    
    # 保存报告到文件
    report_file = "interface_sync_report.md"
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"📝 同步报告已保存到: {report_file}")
    except Exception as e:
        print(f"❌ 保存报告失败: {e}")
    
    print()
    print("=" * 70)
    print("🎉 接口同步完成!")
    print(f"✅ 成功添加 {len(unique_interfaces)} 个独有接口")
    print("📋 建议进行后续的功能验证和文档整理")
    print("=" * 70)

if __name__ == "__main__":
    main()
