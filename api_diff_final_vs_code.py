#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API接口差异对比工具 - apitest-final.mdc vs apitest-code.mdc
检查两个文档的API接口地址差异
检查规则：空格+http协议+空格+api接口地址
"""

import re
from typing import List, Dict, Set, Tu<PERSON>

def extract_api_interfaces_from_final_file(file_path: str) -> List[Tuple[str, int, str]]:
    """
    从 apitest-final.mdc 文件中提取API接口
    格式：#### 步骤X: Y.Z 接口名称 HTTP方法 /api/路径
    返回: [(接口定义, 行号, 完整行内容), ...]
    """
    interfaces = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 匹配格式：空格+http协议+空格+api接口地址
        pattern = r' (GET|POST|PUT|DELETE|PATCH) (/api/[^\s]+)'
        
        for line_num, line in enumerate(lines, 1):
            match = re.search(pattern, line)
            if match:
                method = match.group(1)
                path = match.group(2)
                interface_key = f"{method} {path}"
                interfaces.append((interface_key, line_num, line.strip()))
                
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
        return []
    except Exception as e:
        print(f"错误：读取文件时发生异常 - {e}")
        return []
    
    return interfaces

def extract_api_interfaces_from_code_file(file_path: str) -> List[Tuple[str, int, str]]:
    """
    从 apitest-code.mdc 文件中提取API接口
    格式：- [ ] **X.Y** 接口名称 `HTTP方法 /api/路径`
    返回: [(接口定义, 行号, 完整行内容), ...]
    """
    interfaces = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 匹配格式：- [ ] **X.Y** 接口名称 `HTTP方法 /api/路径`
        pattern = r'- \[ \] \*\*\d+\.\d+\*\* .* `(GET|POST|PUT|DELETE|PATCH) (/api/[^`]+)`'
        
        for line_num, line in enumerate(lines, 1):
            match = re.search(pattern, line)
            if match:
                method = match.group(1)
                path = match.group(2)
                interface_key = f"{method} {path}"
                interfaces.append((interface_key, line_num, line.strip()))
                
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
        return []
    except Exception as e:
        print(f"错误：读取文件时发生异常 - {e}")
        return []
    
    return interfaces

def find_differences(final_interfaces: List[Tuple[str, int, str]], 
                    code_interfaces: List[Tuple[str, int, str]]) -> Dict[str, List]:
    """
    找出两个文件中的接口差异，去除重复
    """
    # 转换为集合进行差异计算，去除重复
    final_set = {interface[0] for interface in final_interfaces}
    code_set = {interface[0] for interface in code_interfaces}
    
    only_in_final_keys = final_set - code_set
    only_in_code_keys = code_set - final_set
    common_keys = final_set & code_set
    
    # 获取详细信息，每个接口只保留第一次出现的位置
    final_dict = {}
    for key, line, content in final_interfaces:
        if key not in final_dict:
            final_dict[key] = (line, content)
    
    code_dict = {}
    for key, line, content in code_interfaces:
        if key not in code_dict:
            code_dict[key] = (line, content)
    
    only_in_final = [(key, final_dict[key][0], final_dict[key][1]) for key in only_in_final_keys]
    only_in_code = [(key, code_dict[key][0], code_dict[key][1]) for key in only_in_code_keys]
    
    return {
        'only_in_final': sorted(only_in_final),
        'only_in_code': sorted(only_in_code),
        'common': sorted(list(common_keys))
    }

def generate_report(differences: Dict[str, List], final_count: int, code_count: int) -> str:
    """
    生成差异对比报告
    """
    report = []
    report.append("# apitest-final.mdc vs apitest-code.mdc API接口差异对比报告")
    report.append("")
    report.append(f"## 统计概览")
    report.append(f"- **apitest-final.mdc**: {final_count} 个接口（去重后）")
    report.append(f"- **apitest-code.mdc**: {code_count} 个接口（去重后）")
    report.append(f"- **共同接口**: {len(differences['common'])} 个")
    report.append(f"- **仅在 final 中**: {len(differences['only_in_final'])} 个")
    report.append(f"- **仅在 code 中**: {len(differences['only_in_code'])} 个")
    report.append(f"- **差异总数**: {len(differences['only_in_final']) + len(differences['only_in_code'])} 个")
    report.append("")
    
    if differences['only_in_final']:
        report.append("## 仅在 apitest-final.mdc 中的接口")
        report.append("")
        for i, (interface, line_num, full_line) in enumerate(differences['only_in_final'], 1):
            report.append(f"### {i}. {interface}")
            report.append(f"- **位置**: 第 {line_num} 行")
            report.append(f"- **内容**: {full_line}")
            report.append("")
    
    if differences['only_in_code']:
        report.append("## 仅在 apitest-code.mdc 中的接口")
        report.append("")
        for i, (interface, line_num, full_line) in enumerate(differences['only_in_code'], 1):
            report.append(f"### {i}. {interface}")
            report.append(f"- **位置**: 第 {line_num} 行")
            report.append(f"- **内容**: {full_line}")
            report.append("")
    
    # 分类统计
    report.append("## 差异分类统计")
    
    if differences['only_in_final']:
        report.append("### apitest-final.mdc 独有接口分类")
        final_methods = {}
        for interface, _, _ in differences['only_in_final']:
            method = interface.split(' ')[0]
            final_methods[method] = final_methods.get(method, 0) + 1
        
        for method, count in sorted(final_methods.items()):
            report.append(f"- {method}: {count} 个")
        report.append("")
    
    if differences['only_in_code']:
        report.append("### apitest-code.mdc 独有接口分类")
        code_methods = {}
        for interface, _, _ in differences['only_in_code']:
            method = interface.split(' ')[0]
            code_methods[method] = code_methods.get(method, 0) + 1
        
        for method, count in sorted(code_methods.items()):
            report.append(f"- {method}: {count} 个")
        report.append("")
    
    report.append("## 分析结论")
    final_more = len(differences['only_in_final']) - len(differences['only_in_code'])
    if final_more > 0:
        report.append(f"✅ **apitest-final.mdc 比 apitest-code.mdc 多 {final_more} 个接口**")
    elif final_more < 0:
        report.append(f"⚠️ **apitest-code.mdc 比 apitest-final.mdc 多 {abs(final_more)} 个接口**")
    else:
        report.append("🎯 **两个文档的接口数量相等，但存在差异**")
    
    report.append("")
    report.append("## 建议")
    if differences['only_in_final'] or differences['only_in_code']:
        report.append("发现接口差异，建议：")
        report.append("1. **同步检查**: 确认差异接口是否为合理的版本差异")
        report.append("2. **功能验证**: 验证独有接口的功能完整性")
        report.append("3. **文档统一**: 考虑是否需要统一两个文档的接口定义")
        report.append("4. **版本管理**: 建立清晰的文档版本管理机制")
    else:
        report.append("两个文档的接口定义完全一致，文档同步良好。")
    
    return "\n".join(report)

def main():
    """主函数"""
    final_file = "apitest-final.mdc"
    code_file = "apitest-code.mdc"
    
    print("🔍 开始对比 apitest-final.mdc 和 apitest-code.mdc 的API接口差异...")
    print(f"📄 文件1: {final_file}")
    print(f"📄 文件2: {code_file}")
    print(f"📏 检查规则: 空格+http协议+空格+api接口地址")
    print()
    
    # 提取两个文件中的API接口
    final_interfaces = extract_api_interfaces_from_final_file(final_file)
    code_interfaces = extract_api_interfaces_from_code_file(code_file)
    
    if not final_interfaces and not code_interfaces:
        print("❌ 未找到任何API接口定义")
        return
    
    # 计算去重后的数量
    final_unique = len(set(interface[0] for interface in final_interfaces))
    code_unique = len(set(interface[0] for interface in code_interfaces))
    
    print(f"📊 apitest-final.mdc: {len(final_interfaces)} 个接口 (去重后: {final_unique} 个)")
    print(f"📊 apitest-code.mdc: {len(code_interfaces)} 个接口 (去重后: {code_unique} 个)")
    
    # 查找差异
    differences = find_differences(final_interfaces, code_interfaces)
    
    # 生成报告
    report = generate_report(differences, final_unique, code_unique)
    
    # 保存报告到文件
    report_file = "api_diff_final_vs_code_report.md"
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"📝 报告已保存到: {report_file}")
    except Exception as e:
        print(f"❌ 保存报告失败: {e}")
    
    # 输出简要结果
    print()
    print("=" * 80)
    final_more = len(differences['only_in_final']) - len(differences['only_in_code'])
    if final_more > 0:
        print(f"✅ apitest-final.mdc 比 apitest-code.mdc 多 {final_more} 个接口")
    elif final_more < 0:
        print(f"⚠️ apitest-code.mdc 比 apitest-final.mdc 多 {abs(final_more)} 个接口")
    else:
        print("🎯 两个文档的接口数量相等，但存在差异")
    
    print(f"📊 共同接口: {len(differences['common'])} 个")
    print(f"📊 仅在 final 中: {len(differences['only_in_final'])} 个")
    print(f"📊 仅在 code 中: {len(differences['only_in_code'])} 个")
    print(f"📊 差异总数: {len(differences['only_in_final']) + len(differences['only_in_code'])} 个")
    print("=" * 80)

if __name__ == "__main__":
    main()
