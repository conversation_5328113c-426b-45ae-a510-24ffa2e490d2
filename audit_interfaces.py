#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CogniAud 接口格式审计工具
对新增的57个接口进行严格的格式规范审计
"""

import re
from typing import List, Dict, Tuple

def audit_new_interfaces():
    """
    审计新增接口的格式规范
    """
    try:
        with open('apitest-code.mdc', 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print("❌ 文件 apitest-code.mdc 不存在")
        return
    
    # 查找新增接口部分
    start_marker = '从 apitest-final.mdc 同步的新增接口'
    if start_marker not in content:
        print("❌ 未找到新增接口部分")
        return
    
    new_section = content.split(start_marker)[1]
    
    print("🔍 CogniAud 接口格式规范审计报告")
    print("=" * 60)
    
    # 审计项目1：接口数量统计
    interface_pattern = r'- \[ \] \*\*52\.(\d+)\*\*'
    interfaces = re.findall(interface_pattern, new_section)
    interface_numbers = [int(num) for num in interfaces]
    
    print(f"📊 审计项目1：接口数量统计")
    print(f"   • 发现接口数量: {len(interfaces)}")
    print(f"   • 接口编号范围: 52.{min(interface_numbers)} - 52.{max(interface_numbers)}")
    print(f"   • 编号连续性: {'✅ 连续' if len(interface_numbers) == max(interface_numbers) - min(interface_numbers) + 1 else '❌ 不连续'}")
    
    # 审计项目2：控制器分类
    controller_pattern = r'### \*\*(\w+Controller) \((\d+)个接口\)\*\*'
    controllers = re.findall(controller_pattern, new_section)
    
    print(f"\n📁 审计项目2：控制器分类")
    print(f"   • 控制器数量: {len(controllers)}")
    
    total_by_controllers = sum(int(count) for _, count in controllers)
    print(f"   • 按控制器统计总数: {total_by_controllers}")
    print(f"   • 数量一致性: {'✅ 一致' if total_by_controllers == len(interfaces) else '❌ 不一致'}")
    
    print(f"   • 控制器分布:")
    for controller, count in controllers:
        print(f"     - {controller}: {count}个接口")
    
    # 审计项目3：格式完整性检查
    print(f"\n📋 审计项目3：格式完整性检查")
    
    # 检查每个接口是否有完整的三个部分
    interface_blocks = re.findall(r'- \[ \] \*\*52\.\d+\*\* .+?(?=- \[ \] \*\*52\.\d+\*\*|### \*\*\w+Controller|$)', new_section, re.DOTALL)
    
    complete_count = 0
    missing_params = []
    missing_success = []
    missing_error = []
    
    for i, block in enumerate(interface_blocks, 1):
        has_params = '请求参数：' in block
        has_success = '成功响应：' in block
        has_error = '错误响应：' in block
        
        if has_params and has_success and has_error:
            complete_count += 1
        else:
            if not has_params:
                missing_params.append(f"52.{i}")
            if not has_success:
                missing_success.append(f"52.{i}")
            if not has_error:
                missing_error.append(f"52.{i}")
    
    print(f"   • 格式完整的接口: {complete_count}/{len(interface_blocks)}")
    print(f"   • 完整性比例: {complete_count/len(interface_blocks)*100:.1f}%")
    
    if missing_params:
        print(f"   • 缺少请求参数: {', '.join(missing_params)}")
    if missing_success:
        print(f"   • 缺少成功响应: {', '.join(missing_success)}")
    if missing_error:
        print(f"   • 缺少错误响应: {', '.join(missing_error)}")
    
    # 审计项目4：接口定义格式检查
    print(f"\n🔧 审计项目4：接口定义格式检查")
    
    # 检查接口定义格式
    interface_def_pattern = r'- \[ \] \*\*52\.\d+\*\* (.+?) `(GET|POST|PUT|DELETE|PATCH) (/api/[^`]+)`'
    interface_defs = re.findall(interface_def_pattern, new_section)
    
    print(f"   • 标准格式接口: {len(interface_defs)}/{len(interfaces)}")
    
    # 检查HTTP方法分布
    methods = {}
    for _, method, _ in interface_defs:
        methods[method] = methods.get(method, 0) + 1
    
    print(f"   • HTTP方法分布:")
    for method, count in sorted(methods.items()):
        print(f"     - {method}: {count}个")
    
    # 审计项目5：路径规范检查
    print(f"\n🛣️ 审计项目5：API路径规范检查")
    
    paths = [path for _, _, path in interface_defs]
    api_paths = [path for path in paths if path.startswith('/api/')]
    
    print(f"   • API路径规范: {len(api_paths)}/{len(paths)} ({'✅ 全部符合' if len(api_paths) == len(paths) else '❌ 存在不规范'})")
    
    # 检查路径重复
    path_counts = {}
    for _, method, path in interface_defs:
        key = f"{method} {path}"
        path_counts[key] = path_counts.get(key, 0) + 1
    
    duplicates = {k: v for k, v in path_counts.items() if v > 1}
    print(f"   • 路径重复检查: {'✅ 无重复' if not duplicates else f'❌ 发现{len(duplicates)}个重复'}")
    
    if duplicates:
        for path, count in duplicates.items():
            print(f"     - {path}: 重复{count}次")
    
    # 最终审计结论
    print(f"\n" + "=" * 60)
    print(f"🎯 CogniAud 最终审计结论")
    
    issues = []
    if len(interfaces) != 57:
        issues.append(f"接口数量不符合预期（期望57个，实际{len(interfaces)}个）")
    if total_by_controllers != len(interfaces):
        issues.append("控制器统计数量与实际接口数量不一致")
    if complete_count != len(interface_blocks):
        issues.append(f"存在格式不完整的接口（{len(interface_blocks) - complete_count}个）")
    if len(api_paths) != len(paths):
        issues.append("存在不符合API路径规范的接口")
    if duplicates:
        issues.append(f"存在重复的接口定义（{len(duplicates)}个）")
    
    if not issues:
        print("✅ 审计通过：所有接口都符合规范要求")
        print("📋 建议：可以进行下一步的功能验证")
    else:
        print("❌ 审计发现问题：")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
        print("📋 建议：需要修正上述问题后重新提交审计")
    
    print("=" * 60)

if __name__ == "__main__":
    audit_new_interfaces()
