<?php

namespace App\Services;

use App\Enums\ApiCodeEnum;
use Carbon\Carbon;
use App\Models\DataExport;
use App\Models\User;
use App\Models\Project;
use App\Models\AiGenerationTask;
use App\Models\CharacterLibrary;
use App\Models\PointsTransaction;
use App\Models\UserFile;
use App\Models\Resource;
use App\Models\ResourceExport;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\Response;
use ZipArchive;

/**
 * 数据导出服务
 */
class DataExportService
{
    /**
     * 创建导出任务
     */
    public function createExport(int $userId, string $exportType, string $exportFormat, array $exportParams = [], array $exportFilters = []): array
    {
        try {
            DB::beginTransaction();

            // 检查用户是否有未完成的导出任务
            $pendingExports = DataExport::byUser($userId)
                ->whereIn('status', [DataExport::STATUS_PENDING, DataExport::STATUS_PROCESSING])
                ->count();

            if ($pendingExports >= 3) {
                return [
                    'code' => ApiCodeEnum::FAIL,
                    'message' => '您有太多未完成的导出任务，请稍后再试',
                    'data' => []
                ];
            }

            // 创建导出任务
            $export = DataExport::create([
                'user_id' => $userId,
                'export_type' => $exportType,
                'export_format' => $exportFormat,
                'export_params' => $exportParams,
                'export_filters' => $exportFilters,
                'status' => DataExport::STATUS_PENDING
            ]);

            DB::commit();

            // 异步执行导出任务
            $this->executeExport($export);

            Log::info('导出任务创建成功', [
                'export_id' => $export->id,
                'user_id' => $userId,
                'export_type' => $exportType,
                'export_format' => $exportFormat
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '导出任务创建成功',
                'data' => [
                    'export_id' => $export->id,
                    'export_type' => $export->export_type,
                    'export_format' => $export->export_format,
                    'status' => $export->status,
                    'created_at' => $export->created_at->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('创建导出任务失败', [
                'user_id' => $userId,
                'export_type' => $exportType,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '创建导出任务失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取用户导出任务列表
     */
    public function getUserExports(int $userId, array $filters, int $page, int $perPage): array
    {
        try {
            $query = DataExport::byUser($userId);

            // 应用筛选条件
            if (!empty($filters['export_type'])) {
                $query->byType($filters['export_type']);
            }

            if (!empty($filters['status'])) {
                $query->byStatus($filters['status']);
            }

            $exports = $query->orderByCreated()->paginate($perPage, ['*'], 'page', $page);

            $exportsData = $exports->map(function ($export) {
                return [
                    'id' => $export->id,
                    'export_type' => $export->export_type,
                    'export_format' => $export->export_format,
                    'status' => $export->status,
                    'file_size' => $export->file_size,
                    'human_file_size' => $export->human_file_size,
                    'record_count' => $export->record_count,
                    'progress_percentage' => $export->getProgressPercentage(),
                    'download_count' => $export->download_count,
                    'is_downloadable' => $export->isDownloadable(),
                    'error_message' => $export->error_message,
                    'created_at' => $export->created_at->format('Y-m-d H:i:s'),
                    'started_at' => $export->started_at?->format('Y-m-d H:i:s'),
                    'completed_at' => $export->completed_at?->format('Y-m-d H:i:s'),
                    'expires_at' => $export->expires_at?->format('Y-m-d H:i:s')
                ];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'exports' => $exportsData,
                    'pagination' => [
                        'current_page' => $exports->currentPage(),
                        'total' => $exports->total(),
                        'per_page' => $exports->perPage(),
                        'last_page' => $exports->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取导出任务列表失败', [
                'user_id' => $userId,
                'filters' => $filters,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '获取导出任务列表失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取导出任务状态
     */
    public function getExportStatus(int $exportId, int $userId): array
    {
        try {
            $export = DataExport::byUser($userId)->find($exportId);

            if (!$export) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '导出任务不存在',
                    'data' => []
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'id' => $export->id,
                    'export_type' => $export->export_type,
                    'export_format' => $export->export_format,
                    'status' => $export->status,
                    'progress' => $export->progress_info ?? [],
                    'error_message' => $export->error_message,
                    'created_at' => $export->created_at->format('Y-m-d H:i:s'),
                    'started_at' => $export->started_at?->format('Y-m-d H:i:s'),
                    'completed_at' => $export->completed_at?->format('Y-m-d H:i:s'),
                    'expires_at' => $export->expires_at?->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取导出任务状态失败', [
                'export_id' => $exportId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '获取导出任务状态失败',
                'data' => []
            ];
        }
    }

    /**
     * 下载导出文件
     */
    public function downloadExport(int $exportId, int $userId): array
    {
        try {
            $export = DataExport::byUser($userId)->find($exportId);

            if (!$export) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '导出任务不存在',
                    'data' => []
                ];
            }

            if (!$export->isDownloadable()) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '文件不可下载',
                    'data' => []
                ];
            }

            // 增加下载次数
            $export->incrementDownload();

            $filename = $this->getExportFilename($export);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'download_url' => Storage::url($export->file_path),
                    'filename' => $filename,
                    'file_size' => $export->file_size,
                    'human_file_size' => $export->human_file_size,
                    'expires_at' => $export->expires_at?->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            Log::error('下载导出文件失败', [
                'export_id' => $exportId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::SYSTEM_ERROR,
                'message' => '下载导出文件失败',
                'data' => []
            ];
        }
    }

    /**
     * 执行导出任务（模拟实现）
     */
    private function executeExport(DataExport $export): void
    {
        try {
            $export->start();

            // 根据导出类型获取数据
            $data = $this->getExportData($export);
            
            // 模拟导出进度
            $export->updateProgress(0, count($data), '开始导出...');
            
            // 模拟处理延迟
            sleep(1);
            
            $export->updateProgress(count($data) / 2, count($data), '导出进行中...');
            sleep(1);
            
            // 生成导出文件
            $filePath = $this->generateExportFile($export, $data);
            $fileSize = Storage::size($filePath);
            
            $export->complete($filePath, $fileSize, count($data));

            Log::info('导出任务完成', [
                'export_id' => $export->id,
                'record_count' => count($data),
                'file_size' => $fileSize
            ]);

        } catch (\Exception $e) {
            $export->fail($e->getMessage());
            
            Log::error('导出任务失败', [
                'export_id' => $export->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取导出数据
     */
    private function getExportData(DataExport $export): array
    {
        switch ($export->export_type) {
            case DataExport::TYPE_USER_DATA:
                return $this->getUserData($export->user_id);
            
            case DataExport::TYPE_PROJECTS:
                return $this->getProjectsData($export->user_id, $export->export_filters);
            
            case DataExport::TYPE_AI_TASKS:
                return $this->getAiTasksData($export->user_id, $export->export_filters);
            
            case DataExport::TYPE_CHARACTERS:
                return $this->getCharactersData($export->user_id, $export->export_filters);
            
            case DataExport::TYPE_POINTS_HISTORY:
                return $this->getPointsHistoryData($export->user_id, $export->export_filters);
            
            case DataExport::TYPE_FILES:
                return $this->getFilesData($export->user_id, $export->export_filters);
            
            default:
                return [];
        }
    }

    /**
     * 生成导出文件
     */
    private function generateExportFile(DataExport $export, array $data): string
    {
        $filename = 'export_' . $export->id . '_' . date('Ymd_His') . '.' . $export->export_format;
        $filePath = 'exports/' . $filename;

        switch ($export->export_format) {
            case DataExport::FORMAT_CSV:
                $content = $this->generateCsv($data);
                break;
            
            case DataExport::FORMAT_JSON:
                $content = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                break;
            
            default:
                $content = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                break;
        }

        Storage::put($filePath, $content);
        
        return $filePath;
    }

    /**
     * 生成CSV内容
     */
    private function generateCsv(array $data): string
    {
        if (empty($data)) {
            return '';
        }

        $csv = '';
        $headers = array_keys($data[0]);
        $csv .= implode(',', $headers) . "\n";

        foreach ($data as $row) {
            $csv .= implode(',', array_map(function($value) {
                return '"' . str_replace('"', '""', $value) . '"';
            }, array_values($row))) . "\n";
        }

        return $csv;
    }

    /**
     * 获取用户数据
     */
    private function getUserData(int $userId): array
    {
        $user = User::find($userId);
        return [
            [
                'id' => $user->id,
                'username' => $user->username,
                'email' => $user->email,
                'points' => $user->points,
                'created_at' => $user->created_at->format('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * 获取项目数据
     */
    private function getProjectsData(int $userId, array $filters): array
    {
        $projects = Project::where('user_id', $userId)->get();
        
        return $projects->map(function($project) {
            return [
                'id' => $project->id,
                'title' => $project->title,
                'description' => $project->description,
                'status' => $project->status,
                'created_at' => $project->created_at->format('Y-m-d H:i:s')
            ];
        })->toArray();
    }

    /**
     * 获取AI任务数据
     */
    private function getAiTasksData(int $userId, array $filters): array
    {
        $tasks = AiGenerationTask::where('user_id', $userId)->get();
        
        return $tasks->map(function($task) {
            return [
                'id' => $task->id,
                'task_type' => $task->task_type,
                'platform' => $task->platform,
                'status' => $task->status,
                'cost' => $task->cost,
                'created_at' => $task->created_at->format('Y-m-d H:i:s')
            ];
        })->toArray();
    }

    /**
     * 获取角色数据
     */
    private function getCharactersData(int $userId, array $filters): array
    {
        // 模拟返回用户绑定的角色数据
        return [
            [
                'id' => 1,
                'name' => '小樱',
                'category' => '动漫角色',
                'binding_date' => Carbon::now()->format('Y-m-d H:i:s')
            ]
        ];
    }

    /**
     * 获取积分历史数据
     */
    private function getPointsHistoryData(int $userId, array $filters): array
    {
        $transactions = PointsTransaction::where('user_id', $userId)->get();

        return $transactions->map(function($transaction) {
            return [
                'id' => $transaction->id,
                'type' => $transaction->type,
                'amount' => $transaction->amount,
                'description' => $transaction->description,
                'created_at' => $transaction->created_at->format('Y-m-d H:i:s')
            ];
        })->toArray();
    }

    /**
     * 获取文件数据
     */
    private function getFilesData(int $userId, array $filters): array
    {
        $files = UserFile::where('user_id', $userId)->get();
        
        return $files->map(function($file) {
            return [
                'id' => $file->id,
                'filename' => $file->filename,
                'original_name' => $file->original_name,
                'file_type' => $file->file_type,
                'file_size' => $file->file_size,
                'created_at' => $file->created_at->format('Y-m-d H:i:s')
            ];
        })->toArray();
    }

    /**
     * 获取导出文件名
     */
    private function getExportFilename(DataExport $export): string
    {
        $typeNames = [
            DataExport::TYPE_USER_DATA => 'user_data',
            DataExport::TYPE_PROJECTS => 'projects',
            DataExport::TYPE_AI_TASKS => 'ai_tasks',
            DataExport::TYPE_CHARACTERS => 'characters',
            DataExport::TYPE_POINTS_HISTORY => 'points_history',
            DataExport::TYPE_FILES => 'files'
        ];

        $typeName = $typeNames[$export->export_type] ?? 'export';
        
        return $typeName . '_export.' . $export->export_format;
    }
}
