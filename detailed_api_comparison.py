#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细API接口对比工具
精确统计和对比 apitest-final.mdc 和 apitest-code.mdc 中的API接口差异
"""

import re
from typing import List, Dict, Set, Tuple
from collections import defaultdict

def extract_api_interfaces_from_final_file(file_path: str) -> List[Tuple[str, int, str]]:
    """
    从 apitest-final.mdc 文件中提取API接口
    返回: [(接口定义, 行号, 完整行内容), ...]
    """
    interfaces = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 匹配格式：空格+http协议+空格+/api/接口地址
        pattern = r' (GET|POST|PUT|DELETE|PATCH) (/api/[^\s]+)'
        
        for line_num, line in enumerate(lines, 1):
            match = re.search(pattern, line)
            if match:
                method = match.group(1)
                path = match.group(2)
                interface_key = f"{method} {path}"
                interfaces.append((interface_key, line_num, line.strip()))
                
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
        return []
    except Exception as e:
        print(f"错误：读取文件时发生异常 - {e}")
        return []
    
    return interfaces

def extract_api_interfaces_from_code_file(file_path: str) -> List[Tuple[str, int, str]]:
    """
    从 apitest-code.mdc 文件中提取API接口
    返回: [(接口定义, 行号, 完整行内容), ...]
    """
    interfaces = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # 匹配格式：`HTTP方法 /api/路径`
        pattern = r'`(GET|POST|PUT|DELETE|PATCH) (/api/[^`]+)`'

        for line_num, line in enumerate(lines, 1):
            match = re.search(pattern, line)
            if match:
                method = match.group(1)
                path = match.group(2)
                interface_key = f"{method} {path}"
                interfaces.append((interface_key, line_num, line.strip()))

    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
        return []
    except Exception as e:
        print(f"错误：读取文件时发生异常 - {e}")
        return []

    return interfaces

def analyze_interface_differences(final_interfaces: List[Tuple[str, int, str]], 
                                code_interfaces: List[Tuple[str, int, str]]) -> Dict:
    """
    分析两个文档的接口差异
    """
    # 转换为字典，便于查找和去重
    final_dict = {}
    for interface_key, line_num, content in final_interfaces:
        if interface_key not in final_dict:
            final_dict[interface_key] = []
        final_dict[interface_key].append((line_num, content))
    
    code_dict = {}
    for interface_key, line_num, content in code_interfaces:
        if interface_key not in code_dict:
            code_dict[interface_key] = []
        code_dict[interface_key].append((line_num, content))
    
    # 获取接口集合
    final_set = set(final_dict.keys())
    code_set = set(code_dict.keys())
    
    # 计算差异
    only_in_final = final_set - code_set
    only_in_code = code_set - final_set
    common_interfaces = final_set & code_set
    
    # 检查重复接口
    final_duplicates = {k: v for k, v in final_dict.items() if len(v) > 1}
    code_duplicates = {k: v for k, v in code_dict.items() if len(v) > 1}
    
    return {
        'final_total': len(final_interfaces),
        'final_unique': len(final_set),
        'code_total': len(code_interfaces),
        'code_unique': len(code_set),
        'only_in_final': only_in_final,
        'only_in_code': only_in_code,
        'common_interfaces': common_interfaces,
        'final_duplicates': final_duplicates,
        'code_duplicates': code_duplicates,
        'final_dict': final_dict,
        'code_dict': code_dict
    }

def print_detailed_analysis(analysis: Dict):
    """
    打印详细的分析结果
    """
    print("🔍 详细API接口对比分析报告")
    print("=" * 80)
    
    # 基础统计
    print(f"📊 基础统计信息:")
    print(f"   • apitest-final.mdc 总接口数: {analysis['final_total']}")
    print(f"   • apitest-final.mdc 去重后: {analysis['final_unique']}")
    print(f"   • apitest-code.mdc 总接口数: {analysis['code_total']}")
    print(f"   • apitest-code.mdc 去重后: {analysis['code_unique']}")
    print(f"   • 共同接口数: {len(analysis['common_interfaces'])}")
    print(f"   • 总差异数: {len(analysis['only_in_final']) + len(analysis['only_in_code'])}")
    
    # 重复接口检查
    print(f"\n🔄 重复接口检查:")
    if analysis['final_duplicates']:
        print(f"   • apitest-final.mdc 重复接口: {len(analysis['final_duplicates'])}个")
        for interface, occurrences in analysis['final_duplicates'].items():
            print(f"     - {interface}: 出现{len(occurrences)}次 (行号: {[occ[0] for occ in occurrences]})")
    else:
        print(f"   • apitest-final.mdc: 无重复接口 ✅")
    
    if analysis['code_duplicates']:
        print(f"   • apitest-code.mdc 重复接口: {len(analysis['code_duplicates'])}个")
        for interface, occurrences in analysis['code_duplicates'].items():
            print(f"     - {interface}: 出现{len(occurrences)}次 (行号: {[occ[0] for occ in occurrences]})")
    else:
        print(f"   • apitest-code.mdc: 无重复接口 ✅")
    
    # 仅在 final 中的接口
    print(f"\n📄 仅在 apitest-final.mdc 中的接口 ({len(analysis['only_in_final'])}个):")
    if analysis['only_in_final']:
        for i, interface in enumerate(sorted(analysis['only_in_final']), 1):
            line_info = analysis['final_dict'][interface][0]
            print(f"   {i:2d}. {interface} (行号: {line_info[0]})")
    else:
        print("   无独有接口")
    
    # 仅在 code 中的接口
    print(f"\n📄 仅在 apitest-code.mdc 中的接口 ({len(analysis['only_in_code'])}个):")
    if analysis['only_in_code']:
        for i, interface in enumerate(sorted(analysis['only_in_code']), 1):
            line_info = analysis['code_dict'][interface][0]
            print(f"   {i:2d}. {interface} (行号: {line_info[0]})")
    else:
        print("   无独有接口")
    
    # HTTP方法统计
    print(f"\n📊 HTTP方法分布统计:")
    
    # final文件方法统计
    final_methods = defaultdict(int)
    for interface in analysis['final_dict'].keys():
        method = interface.split()[0]
        final_methods[method] += 1
    
    # code文件方法统计
    code_methods = defaultdict(int)
    for interface in analysis['code_dict'].keys():
        method = interface.split()[0]
        code_methods[method] += 1
    
    print(f"   • apitest-final.mdc:")
    for method in sorted(final_methods.keys()):
        print(f"     - {method}: {final_methods[method]}个")
    
    print(f"   • apitest-code.mdc:")
    for method in sorted(code_methods.keys()):
        print(f"     - {method}: {code_methods[method]}个")
    
    # 路径前缀统计
    print(f"\n🛣️ API路径前缀统计:")
    
    def get_path_prefix(interface):
        path = interface.split()[1]
        parts = path.split('/')
        if len(parts) >= 3:
            return f"/{parts[1]}/{parts[2]}"
        return path
    
    final_prefixes = defaultdict(int)
    for interface in analysis['final_dict'].keys():
        prefix = get_path_prefix(interface)
        final_prefixes[prefix] += 1
    
    code_prefixes = defaultdict(int)
    for interface in analysis['code_dict'].keys():
        prefix = get_path_prefix(interface)
        code_prefixes[prefix] += 1
    
    all_prefixes = set(final_prefixes.keys()) | set(code_prefixes.keys())
    
    print(f"   路径前缀对比:")
    for prefix in sorted(all_prefixes):
        final_count = final_prefixes.get(prefix, 0)
        code_count = code_prefixes.get(prefix, 0)
        diff = abs(final_count - code_count)
        status = "✅" if final_count == code_count else f"❌ (差异:{diff})"
        print(f"     {prefix}: final({final_count}) vs code({code_count}) {status}")

def generate_sync_recommendations(analysis: Dict):
    """
    生成同步建议
    """
    print(f"\n" + "=" * 80)
    print(f"💡 同步建议:")
    
    if len(analysis['only_in_final']) > 0:
        print(f"   1. 需要将 {len(analysis['only_in_final'])} 个接口从 final 同步到 code")
    
    if len(analysis['only_in_code']) > 0:
        print(f"   2. 需要将 {len(analysis['only_in_code'])} 个接口从 code 同步到 final")
    
    if analysis['final_duplicates']:
        print(f"   3. 需要清理 final 文件中的 {len(analysis['final_duplicates'])} 个重复接口")
    
    if analysis['code_duplicates']:
        print(f"   4. 需要清理 code 文件中的 {len(analysis['code_duplicates'])} 个重复接口")
    
    total_issues = len(analysis['only_in_final']) + len(analysis['only_in_code']) + len(analysis['final_duplicates']) + len(analysis['code_duplicates'])
    
    if total_issues == 0:
        print(f"   ✅ 两个文档已完全同步，无需额外操作")
    else:
        print(f"   📋 总计需要处理 {total_issues} 个问题")

def main():
    """主函数"""
    final_file = "apitest-final.mdc"
    code_file = "apitest-code.mdc"
    
    print("🔧 开始详细API接口对比分析...")
    print(f"📄 文件1: {final_file}")
    print(f"📄 文件2: {code_file}")
    print()
    
    # 提取两个文件中的API接口
    final_interfaces = extract_api_interfaces_from_final_file(final_file)
    code_interfaces = extract_api_interfaces_from_code_file(code_file)
    
    if not final_interfaces:
        print("❌ 未找到 final 文件中的API接口定义")
        return
    
    if not code_interfaces:
        print("❌ 未找到 code 文件中的API接口定义")
        return
    
    # 分析差异
    analysis = analyze_interface_differences(final_interfaces, code_interfaces)
    
    # 打印详细分析
    print_detailed_analysis(analysis)
    
    # 生成同步建议
    generate_sync_recommendations(analysis)
    
    print("=" * 80)

if __name__ == "__main__":
    main()
