#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API接口差异检测工具
比较 apitest-code.mdc 和 apitest-url.mdc 中的API接口差异
"""

import re
from typing import List, Set, Dict, Tu<PERSON>

def extract_api_interfaces_from_url_file(file_path: str) -> List[Tuple[str, int, str]]:
    """
    从 apitest-url.mdc 文件中提取API接口
    格式：步骤X: Y.Z 接口名称 HTTP方法 /api/路径
    返回: [(接口定义, 行号, 完整行内容), ...]
    """
    interfaces = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # 匹配格式：步骤X: Y.Z 接口名称 HTTP方法 /api/路径
        pattern = r'步骤\d+: \d+\.\d+ .* (GET|POST|PUT|DELETE|PATCH) (/api/[^\s]+)'

        for line_num, line in enumerate(lines, 1):
            match = re.search(pattern, line)
            if match:
                method = match.group(1)
                path = match.group(2)
                interface_key = f"{method} {path}"
                interfaces.append((interface_key, line_num, line.strip()))

    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
        return []
    except Exception as e:
        print(f"错误：读取文件时发生异常 - {e}")
        return []

    return interfaces

def extract_api_interfaces_from_code_file(file_path: str) -> List[Tuple[str, int, str]]:
    """
    从 apitest-code.mdc 文件中提取API接口
    格式：- [ ] **X.Y** 接口名称 `HTTP方法 /api/路径`
    返回: [(接口定义, 行号, 完整行内容), ...]
    """
    interfaces = []

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # 匹配格式：- [ ] **X.Y** 接口名称 `HTTP方法 /api/路径`
        pattern = r'- \[ \] \*\*\d+\.\d+\*\* .* `(GET|POST|PUT|DELETE|PATCH) (/api/[^`]+)`'

        for line_num, line in enumerate(lines, 1):
            match = re.search(pattern, line)
            if match:
                method = match.group(1)
                path = match.group(2)
                interface_key = f"{method} {path}"
                interfaces.append((interface_key, line_num, line.strip()))

    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
        return []
    except Exception as e:
        print(f"错误：读取文件时发生异常 - {e}")
        return []

    return interfaces

def find_differences(code_interfaces: List[Tuple[str, int, str]], url_interfaces: List[Tuple[str, int, str]]) -> Dict[str, List]:
    """
    找出两个文件中的接口差异，去除重复
    """
    # 转换为集合进行差异计算，去除重复
    code_set = {interface[0] for interface in code_interfaces}
    url_set = {interface[0] for interface in url_interfaces}

    only_in_code_keys = code_set - url_set
    only_in_url_keys = url_set - code_set
    common_keys = code_set & url_set

    # 获取详细信息，每个接口只保留第一次出现的位置
    code_dict = {}
    for key, line, content in code_interfaces:
        if key not in code_dict:
            code_dict[key] = (line, content)

    url_dict = {}
    for key, line, content in url_interfaces:
        if key not in url_dict:
            url_dict[key] = (line, content)

    only_in_code = [(key, code_dict[key][0], code_dict[key][1]) for key in only_in_code_keys]
    only_in_url = [(key, url_dict[key][0], url_dict[key][1]) for key in only_in_url_keys]

    return {
        'only_in_code': sorted(only_in_code),
        'only_in_url': sorted(only_in_url),
        'common': sorted(list(common_keys))
    }

def generate_report(differences: Dict[str, List], code_count: int, url_count: int) -> str:
    """
    生成差异报告
    """
    report = []
    report.append("# API接口差异分析报告")
    report.append("")
    report.append(f"## 统计概览")
    report.append(f"- **apitest-code.mdc**: {code_count} 个接口")
    report.append(f"- **apitest-url.mdc**: {url_count} 个接口")
    report.append(f"- **共同接口**: {len(differences['common'])} 个")
    report.append(f"- **仅在 code 中**: {len(differences['only_in_code'])} 个")
    report.append(f"- **仅在 url 中**: {len(differences['only_in_url'])} 个")
    report.append("")

    if differences['only_in_code']:
        report.append("## 仅在 apitest-code.mdc 中的接口")
        report.append("")
        for i, (interface, line_num, full_line) in enumerate(differences['only_in_code'], 1):
            report.append(f"{i}. `{interface}`")
            report.append(f"   - 位置: 第 {line_num} 行")
            report.append(f"   - 内容: {full_line}")
            report.append("")

    if differences['only_in_url']:
        report.append("## 仅在 apitest-url.mdc 中的接口")
        report.append("")
        for i, (interface, line_num, full_line) in enumerate(differences['only_in_url'], 1):
            report.append(f"{i}. `{interface}`")
            report.append(f"   - 位置: 第 {line_num} 行")
            report.append(f"   - 内容: {full_line}")
            report.append("")

    report.append("## 分析结论")
    if len(differences['only_in_code']) == 3:
        report.append("✅ **确认发现3个多出的接口**")
        report.append("")
        report.append("apitest-code.mdc 比 apitest-url.mdc 多出的3个接口：")
        for i, (interface, line_num, full_line) in enumerate(differences['only_in_code'], 1):
            report.append(f"  {i}. {interface} (第{line_num}行)")
    else:
        report.append(f"⚠️ **实际差异数量**: {len(differences['only_in_code'])} 个")
        if len(differences['only_in_code']) > 3:
            report.append("多于预期的3个接口，可能存在其他差异。")
        else:
            report.append("少于预期的3个接口，需要进一步检查。")

    return "\n".join(report)

def main():
    """主函数"""
    code_file = "apitest-code.mdc"
    url_file = "apitest-url.mdc"
    
    print("🔍 开始分析API接口差异...")
    print(f"📄 比较文件: {code_file} vs {url_file}")
    print()
    
    # 提取两个文件中的API接口
    code_interfaces = extract_api_interfaces_from_code_file(code_file)
    url_interfaces = extract_api_interfaces_from_url_file(url_file)
    
    if not code_interfaces and not url_interfaces:
        print("❌ 未找到任何API接口定义")
        return
    
    # 计算去重后的数量
    code_unique = len(set(interface[0] for interface in code_interfaces))
    url_unique = len(set(interface[0] for interface in url_interfaces))

    print(f"📊 apitest-code.mdc: {len(code_interfaces)} 个接口 (去重后: {code_unique} 个)")
    print(f"📊 apitest-url.mdc: {len(url_interfaces)} 个接口 (去重后: {url_unique} 个)")
    
    # 查找差异
    differences = find_differences(code_interfaces, url_interfaces)
    
    # 生成报告
    report = generate_report(differences, code_unique, url_unique)
    
    # 保存报告到文件
    report_file = "api_diff_report.md"
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"📝 报告已保存到: {report_file}")
    except Exception as e:
        print(f"❌ 保存报告失败: {e}")
    
    # 输出简要结果
    print()
    print("=" * 60)
    if len(differences['only_in_code']) == 3:
        print("✅ 找到用户要求的3个多出接口:")
        for i, (interface, line_num, full_line) in enumerate(differences['only_in_code'], 1):
            print(f"  {i}. {interface} (第{line_num}行)")
    else:
        print(f"📈 apitest-code.mdc 多出 {len(differences['only_in_code'])} 个接口")
        if differences['only_in_code']:
            print("多出的接口:")
            for i, (interface, line_num, full_line) in enumerate(differences['only_in_code'], 1):
                print(f"  {i}. {interface} (第{line_num}行)")
    print("=" * 60)

if __name__ == "__main__":
    main()
