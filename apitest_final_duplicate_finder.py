#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API接口重复检测工具 - 专门用于 apitest-final.mdc
查找重复的API接口地址
查找规则：空格+http协议+空格+api接口地址
报告：第几行和http协议+api接口地址
"""

import re
from collections import defaultdict
from typing import List, Dict, <PERSON><PERSON>

def extract_api_interfaces_from_final_file(file_path: str) -> List[Tuple[str, int, str]]:
    """
    从 apitest-final.mdc 文件中提取API接口
    格式：#### 步骤X: Y.Z 接口名称 HTTP方法 /api/路径
    返回: [(接口定义, 行号, 完整行内容), ...]
    """
    interfaces = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 匹配格式：#### 步骤X: Y.Z 接口名称 HTTP方法 /api/路径
        # 查找规则：空格+http协议+空格+api接口地址
        pattern = r' (GET|POST|PUT|DELETE|PATCH) (/api/[^\s]+)'
        
        for line_num, line in enumerate(lines, 1):
            match = re.search(pattern, line)
            if match:
                method = match.group(1)
                path = match.group(2)
                interface_key = f"{method} {path}"
                interfaces.append((interface_key, line_num, line.strip()))
                
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
        return []
    except Exception as e:
        print(f"错误：读取文件时发生异常 - {e}")
        return []
    
    return interfaces

def find_duplicates(interfaces: List[Tuple[str, int, str]]) -> Dict[str, List[Tuple[int, str]]]:
    """
    查找重复的API接口
    返回: {接口定义: [(行号, 完整行内容), ...], ...}
    """
    interface_map = defaultdict(list)
    
    for interface_key, line_num, full_line in interfaces:
        interface_map[interface_key].append((line_num, full_line))
    
    # 只返回出现次数大于1的接口
    duplicates = {k: v for k, v in interface_map.items() if len(v) > 1}
    
    return duplicates

def generate_report(duplicates: Dict[str, List[Tuple[int, str]]], total_interfaces: int) -> str:
    """
    生成重复接口检测报告
    """
    report = []
    report.append("# apitest-final.mdc 重复API接口检测报告")
    report.append("")
    report.append(f"## 检测概览")
    report.append(f"- **总接口数量**: {total_interfaces}")
    report.append(f"- **重复接口类型数量**: {len(duplicates)}")
    
    total_duplicate_instances = sum(len(instances) for instances in duplicates.values())
    report.append(f"- **重复接口实例总数**: {total_duplicate_instances}")
    report.append(f"- **去重后接口数量**: {total_interfaces - (total_duplicate_instances - len(duplicates))}")
    report.append("")
    
    if not duplicates:
        report.append("## 检测结果")
        report.append("✅ **未发现重复的API接口**")
        report.append("")
        report.append("所有API接口都是唯一的，没有重复定义。")
    else:
        report.append("## 重复接口详情")
        report.append("")
        
        for i, (interface_key, instances) in enumerate(duplicates.items(), 1):
            report.append(f"### {i}. {interface_key}")
            report.append(f"**重复次数**: {len(instances)}")
            report.append("")
            report.append("**出现位置**:")
            
            for j, (line_num, full_line) in enumerate(instances, 1):
                report.append(f"{j}. **第 {line_num} 行**: `{interface_key}`")
                report.append(f"   ```")
                report.append(f"   {full_line}")
                report.append(f"   ```")
                report.append("")
            
            report.append("---")
            report.append("")
    
    report.append("## 统计分析")
    if duplicates:
        report.append("### 重复接口分类统计")
        
        # 按HTTP方法分类
        method_stats = defaultdict(int)
        path_stats = defaultdict(int)
        
        for interface_key, instances in duplicates.items():
            method = interface_key.split(' ')[0]
            path = interface_key.split(' ', 1)[1]
            method_stats[method] += len(instances)
            path_stats[path] += len(instances)
        
        report.append("")
        report.append("**按HTTP方法分类**:")
        for method, count in sorted(method_stats.items()):
            report.append(f"- {method}: {count} 个重复实例")
        
        report.append("")
        report.append("**重复最多的路径**:")
        sorted_paths = sorted(path_stats.items(), key=lambda x: x[1], reverse=True)
        for path, count in sorted_paths[:10]:  # 显示前10个
            report.append(f"- {path}: {count} 次重复")
    
    report.append("")
    report.append("## 简要列表")
    if duplicates:
        report.append("### 重复接口简要列表（按要求格式）")
        report.append("")
        for interface_key, instances in duplicates.items():
            report.append(f"**{interface_key}**:")
            for line_num, full_line in instances:
                report.append(f"- 第 {line_num} 行: {interface_key}")
            report.append("")
    
    report.append("## 建议")
    if duplicates:
        report.append("发现重复的API接口定义，建议：")
        report.append("1. **立即清理**: 删除重复的接口定义，保留功能最完整的版本")
        report.append("2. **统一管理**: 建立接口定义的统一管理机制")
        report.append("3. **自动检测**: 在文档更新流程中加入重复检测步骤")
        report.append("4. **版本控制**: 使用版本控制跟踪接口变更")
        report.append("5. **文档整合**: 合并相关功能的接口定义")
    else:
        report.append("所有API接口定义都是唯一的，文档结构良好。")
    
    return "\n".join(report)

def main():
    """主函数"""
    file_path = "apitest-final.mdc"
    
    print("🔍 开始检测 apitest-final.mdc 中的重复API接口...")
    print(f"📄 分析文件: {file_path}")
    print(f"📏 查找规则: 空格+http协议+空格+api接口地址")
    print(f"📋 报告格式: 第几行和http协议+api接口地址")
    print()
    
    # 提取所有API接口
    interfaces = extract_api_interfaces_from_final_file(file_path)
    
    if not interfaces:
        print("❌ 未找到任何API接口定义")
        return
    
    print(f"📊 找到 {len(interfaces)} 个API接口定义")
    
    # 查找重复接口
    duplicates = find_duplicates(interfaces)
    
    # 生成报告
    report = generate_report(duplicates, len(interfaces))
    
    # 保存报告到文件
    report_file = "apitest_final_duplicate_report.md"
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"📝 报告已保存到: {report_file}")
    except Exception as e:
        print(f"❌ 保存报告失败: {e}")
    
    # 输出简要结果（按用户要求的格式）
    print()
    print("=" * 70)
    if duplicates:
        print(f"⚠️  发现 {len(duplicates)} 种重复的API接口")
        total_duplicates = sum(len(instances) for instances in duplicates.values())
        print(f"📊 重复实例总数: {total_duplicates}")
        print(f"🔧 需要清理的重复项: {total_duplicates - len(duplicates)}")
        print()
        print("重复的接口详情（按要求格式）:")
        for interface_key, instances in duplicates.items():
            print(f"\n🔄 {interface_key} (重复 {len(instances)} 次):")
            for line_num, full_line in instances:
                print(f"   • 第 {line_num} 行: {interface_key}")
    else:
        print("✅ 未发现重复的API接口")
        print("📋 所有接口定义都是唯一的")
    print("=" * 70)

if __name__ == "__main__":
    main()
