# API接口差异分析报告

## 统计概览
- **apitest-code.mdc**: 268 个接口
- **apitest-url.mdc**: 274 个接口
- **共同接口**: 212 个
- **仅在 code 中**: 56 个
- **仅在 url 中**: 62 个

## 仅在 apitest-code.mdc 中的接口

1. `DELETE /api/exports/{id}`
   - 位置: 第 868 行
   - 内容: - [ ] **34.6** 删除导出任务 `DELETE /api/exports/{id}`

2. `DELETE /api/projects/{id}`
   - 位置: 第 1002 行
   - 内容: - [ ] **39.8** 删除项目 `DELETE /api/projects/{id}`

3. `DELETE /api/publications/{id}`
   - 位置: 第 1046 行
   - 内容: - [ ] **41.4** 取消发布 `DELETE /api/publications/{id}`

4. `DELETE /api/versions/{id}`
   - 位置: 第 380 行
   - 内容: - [ ] **11.5** 删除版本 `DELETE /api/versions/{id}`

5. `DELETE /api/works/{id}`
   - 位置: 第 1302 行
   - 内容: - [ ] **49.3** 删除作品 `DELETE /api/works/{id}`

6. `GET /api/ai-models/usage-stats`
   - 位置: 第 410 行
   - 内容: - [ ] **12.2** 获取使用统计 `GET /api/ai-models/usage-stats`

7. `GET /api/analytics/ai-performance`
   - 位置: 第 454 行
   - 内容: - [ ] **14.3** 获取AI平台性能分析 `GET /api/analytics/ai-performance`

8. `GET /api/analytics/revenue`
   - 位置: 第 462 行
   - 内容: - [ ] **14.5** 获取收入分析 `GET /api/analytics/revenue`

9. `GET /api/analytics/system-usage`
   - 位置: 第 450 行
   - 内容: - [ ] **14.2** 获取系统使用统计 `GET /api/analytics/system-usage`

10. `GET /api/analytics/user-retention`
   - 位置: 第 458 行
   - 内容: - [ ] **14.4** 获取用户留存分析 `GET /api/analytics/user-retention`

11. `GET /api/files/{id}`
   - 位置: 第 886 行
   - 内容: - [ ] **35.3** 文件详情 `GET /api/files/{id}`

12. `GET /api/images/history`
   - 位置: 第 920 行
   - 内容: - [ ] **36.6** 图像生成历史 `GET /api/images/history`

13. `GET /api/logs/ai-calls`
   - 位置: 第 934 行
   - 内容: - [ ] **37.3** 查询AI调用日志 `GET /api/logs/ai-calls`

14. `GET /api/monitor/alerts`
   - 位置: 第 350 行
   - 内容: - [ ] **10.4** 系统告警列表 `GET /api/monitor/alerts`

15. `GET /api/monitor/metrics`
   - 位置: 第 342 行
   - 内容: - [ ] **10.2** 系统性能指标 `GET /api/monitor/metrics`

16. `GET /api/monitor/realtime`
   - 位置: 第 346 行
   - 内容: - [ ] **10.3** 实时监控数据 `GET /api/monitor/realtime`

17. `GET /api/projects/list`
   - 位置: 第 990 行
   - 内容: - [ ] **39.5** 获取项目列表 `GET /api/projects/list`

18. `GET /api/projects/{id}/detail`
   - 位置: 第 1020 行
   - 内容: - [ ] **40.4** 获取项目详情 `GET /api/projects/{id}/detail`

19. `GET /api/publications/{id}/status`
   - 位置: 第 1038 行
   - 内容: - [ ] **41.2** 获取发布状态 `GET /api/publications/{id}/status`

20. `GET /api/recommendations/analytics`
   - 位置: 第 1096 行
   - 内容: - [ ] **42.7** 获取推荐统计 `GET /api/recommendations/analytics`

21. `GET /api/recommendations/content`
   - 位置: 第 1072 行
   - 内容: - [ ] **42.1** 获取内容推荐 `GET /api/recommendations/content`

22. `GET /api/recommendations/preferences`
   - 位置: 第 1088 行
   - 内容: - [ ] **42.5** 获取推荐设置 `GET /api/recommendations/preferences`

23. `GET /api/recommendations/topics`
   - 位置: 第 1080 行
   - 内容: - [ ] **42.3** 获取话题推荐 `GET /api/recommendations/topics`

24. `GET /api/recommendations/users`
   - 位置: 第 1076 行
   - 内容: - [ ] **42.2** 获取用户推荐 `GET /api/recommendations/users`

25. `GET /api/resources/{id}/status`
   - 位置: 第 1110 行
   - 内容: - [ ] **43.2** 资源生成状态查询 `GET /api/resources/{id}/status`

26. `GET /api/resources/{id}/versions`
   - 位置: 第 368 行
   - 内容: - [ ] **11.2** 获取版本历史 `GET /api/resources/{id}/versions`

27. `GET /api/versions/compare`
   - 位置: 第 384 行
   - 内容: - [ ] **11.6** 版本比较 `GET /api/versions/compare`

28. `GET /api/versions/{id}`
   - 位置: 第 372 行
   - 内容: - [ ] **11.3** 获取版本详情 `GET /api/versions/{id}`

29. `GET /api/videos/platform-comparison`
   - 位置: 第 1242 行
   - 内容: - [ ] **47.3** 视频平台性能对比 `GET /api/videos/platform-comparison`

30. `GET /api/websocket/sessions`
   - 位置: 第 274 行
   - 内容: - [ ] **7.2** 获取WebSocket会话列表 `GET /api/websocket/sessions`

31. `GET /api/works/trending`
   - 位置: 第 1322 行
   - 内容: - [ ] **49.8** 热门作品 `GET /api/works/trending`

32. `GET /api/works/{id}/share`
   - 位置: 第 1314 行
   - 内容: - [ ] **49.6** 获取分享链接 `GET /api/works/{id}/share`

33. `POST /api/ai-models/{model_id}/favorite`
   - 位置: 第 414 行
   - 内容: - [ ] **12.3** 收藏模型 `POST /api/ai-models/{model_id}/favorite`

34. `POST /api/ai-models/{model_id}/test`
   - 位置: 第 406 行
   - 内容: - [ ] **12.1** 测试模型 `POST /api/ai-models/{model_id}/test`

35. `POST /api/ai/text/generate`
   - 位置: 第 742 行
   - 内容: - [ ] **29.1** 文本生成 `POST /api/ai/text/generate`

36. `POST /api/analytics/custom-report`
   - 位置: 第 466 行
   - 内容: - [ ] **14.6** 生成自定义报告 `POST /api/analytics/custom-report`

37. `POST /api/exports/batch`
   - 位置: 第 872 行
   - 内容: - [ ] **34.7** 批量导出 `POST /api/exports/batch`

38. `POST /api/exports/{id}/cancel`
   - 位置: 第 864 行
   - 内容: - [ ] **34.5** 取消导出任务 `POST /api/exports/{id}/cancel`

39. `POST /api/images/batch-generate`
   - 位置: 第 912 行
   - 内容: - [ ] **36.4** 批量图像生成 `POST /api/images/batch-generate`

40. `POST /api/images/{task_id}/switch-platform`
   - 位置: 第 908 行
   - 内容: - [ ] **36.3** 平台切换 `POST /api/images/{task_id}/switch-platform`

41. `POST /api/projects/create`
   - 位置: 第 994 行
   - 内容: - [ ] **39.6** 创建项目 `POST /api/projects/create`

42. `POST /api/projects/{id}/collaboration`
   - 位置: 第 1012 行
   - 内容: - [ ] **40.2** 项目协作管理 `POST /api/projects/{id}/collaboration`

43. `POST /api/publications/{id}/unpublish`
   - 位置: 第 1050 行
   - 内容: - [ ] **41.5** 取消发布(POST方式) `POST /api/publications/{id}/unpublish`

44. `POST /api/recommendations/feedback`
   - 位置: 第 1084 行
   - 内容: - [ ] **42.4** 反馈推荐 `POST /api/recommendations/feedback`

45. `POST /api/resources/generate`
   - 位置: 第 1106 行
   - 内容: - [ ] **43.1** 资源生成任务创建 `POST /api/resources/generate`

46. `POST /api/resources/{id}/versions`
   - 位置: 第 364 行
   - 内容: - [ ] **11.1** 创建资源版本 `POST /api/resources/{id}/versions`

47. `POST /api/websocket/disconnect`
   - 位置: 第 278 行
   - 内容: - [ ] **7.3** 断开WebSocket连接 `POST /api/websocket/disconnect`

48. `POST /api/works/{id}/like`
   - 位置: 第 1318 行
   - 内容: - [ ] **49.7** 点赞作品 `POST /api/works/{id}/like`

49. `PUT /api/logs/errors/{id}/resolve`
   - 位置: 第 942 行
   - 内容: - [ ] **37.5** 标记错误为已解决 `PUT /api/logs/errors/{id}/resolve`

50. `PUT /api/monitor/alerts/{id}/acknowledge`
   - 位置: 第 354 行
   - 内容: - [ ] **10.5** 确认告警 `PUT /api/monitor/alerts/{id}/acknowledge`

51. `PUT /api/monitor/alerts/{id}/resolve`
   - 位置: 第 358 行
   - 内容: - [ ] **10.6** 解决告警 `PUT /api/monitor/alerts/{id}/resolve`

52. `PUT /api/projects/{id}`
   - 位置: 第 998 行
   - 内容: - [ ] **39.7** 更新项目 `PUT /api/projects/{id}`

53. `PUT /api/publications/{id}`
   - 位置: 第 1042 行
   - 内容: - [ ] **41.3** 更新作品信息 `PUT /api/publications/{id}`

54. `PUT /api/recommendations/preferences`
   - 位置: 第 1092 行
   - 内容: - [ ] **42.6** 更新推荐设置 `PUT /api/recommendations/preferences`

55. `PUT /api/versions/{id}/set-current`
   - 位置: 第 376 行
   - 内容: - [ ] **11.4** 设置当前版本 `PUT /api/versions/{id}/set-current`

56. `PUT /api/works/{id}`
   - 位置: 第 1298 行
   - 内容: - [ ] **49.2** 编辑作品 `PUT /api/works/{id}`

## 仅在 apitest-url.mdc 中的接口

1. `DELETE /api/batch/delete`
   - 位置: 第 375 行
   - 内容: 步骤1: 34.1 批量删除              DELETE /api/batch/delete

2. `DELETE /api/batch/{id}`
   - 位置: 第 379 行
   - 内容: 步骤5: 34.5 取消批量操作          DELETE /api/batch/{id}

3. `DELETE /api/downloads/{id}`
   - 位置: 第 368 行
   - 内容: 步骤5: 33.5 取消下载              DELETE /api/downloads/{id}

4. `DELETE /api/export/{id}`
   - 位置: 第 348 行
   - 内容: 步骤10: 31.6 取消导出任务         DELETE /api/export/{id}

5. `DELETE /api/general-exports/{id}`
   - 位置: 第 567 行
   - 内容: 步骤6: 34.6 删除导出任务          DELETE /api/general-exports/{id}

6. `DELETE /api/logs/cleanup`
   - 位置: 第 406 行
   - 内容: 步骤5: 37.5 清理日志              DELETE /api/logs/cleanup

7. `GET /api/ads/config`
   - 位置: 第 422 行
   - 内容: 步骤1: 42.1 获取广告配置          GET /api/ads/config

8. `GET /api/analytics/content`
   - 位置: 第 395 行
   - 内容: 步骤4: 36.10 获取内容分析         GET /api/analytics/content

9. `GET /api/analytics/performance`
   - 位置: 第 393 行
   - 内容: 步骤2: 36.8 获取性能分析          GET /api/analytics/performance

10. `GET /api/analytics/usage`
   - 位置: 第 392 行
   - 内容: 步骤1: 36.7 获取使用分析          GET /api/analytics/usage

11. `GET /api/app-monitor/alerts`
   - 位置: 第 280 行
   - 内容: 步骤10: 11.4 应用告警列表        GET /api/app-monitor/alerts

12. `GET /api/app-monitor/metrics`
   - 位置: 第 278 行
   - 内容: 步骤8: 11.2 应用性能指标         GET /api/app-monitor/metrics

13. `GET /api/app-monitor/realtime`
   - 位置: 第 279 行
   - 内容: 步骤9: 11.3 实时监控数据         GET /api/app-monitor/realtime

14. `GET /api/batch/{id}/status`
   - 位置: 第 378 行
   - 内容: 步骤4: 34.4 获取批量操作状态      GET /api/batch/{id}/status

15. `GET /api/data-export/{id}/download`
   - 位置: 第 342 行
   - 内容: 步骤4: 30.4 下载导出文件          GET /api/data-export/{id}/download

16. `GET /api/data-export/{id}/status`
   - 位置: 第 341 行
   - 内容: 步骤3: 30.3 获取导出状态          GET /api/data-export/{id}/status

17. `GET /api/downloads/history`
   - 位置: 第 369 行
   - 内容: 步骤6: 33.6 获取下载历史          GET /api/downloads/history

18. `GET /api/downloads/{id}/status`
   - 位置: 第 365 行
   - 内容: 步骤2: 33.2 获取下载状态          GET /api/downloads/{id}/status

19. `GET /api/export/history`
   - 位置: 第 347 行
   - 内容: 步骤9: 31.5 获取导出历史          GET /api/export/history

20. `GET /api/files/{id}/info`
   - 位置: 第 355 行
   - 内容: 步骤2: 32.2 获取文件信息          GET /api/files/{id}/info

21. `GET /api/files/{id}/preview`
   - 位置: 第 358 行
   - 内容: 步骤5: 32.5 文件预览              GET /api/files/{id}/preview

22. `GET /api/general-exports/list`
   - 位置: 第 565 行
   - 内容: 步骤4: 34.4 导出任务列表          GET /api/general-exports/list

23. `GET /api/general-exports/{id}/download`
   - 位置: 第 564 行
   - 内容: 步骤3: 34.3 下载导出文件          GET /api/general-exports/{id}/download

24. `GET /api/general-exports/{id}/status`
   - 位置: 第 563 行
   - 内容: 步骤2: 34.2 获取导出状态          GET /api/general-exports/{id}/status

25. `GET /api/logs/api-calls`
   - 位置: 第 405 行
   - 内容: 步骤4: 37.4 获取API调用日志       GET /api/logs/api-calls

26. `GET /api/project-management/milestones`
   - 位置: 第 417 行
   - 内容: 步骤6: 40.6 项目里程碑            GET /api/project-management/milestones

27. `GET /api/project-management/progress`
   - 位置: 第 413 行
   - 内容: 步骤2: 40.2 获取项目进度          GET /api/project-management/progress

28. `GET /api/project-management/statistics`
   - 位置: 第 415 行
   - 内容: 步骤4: 40.4 获取项目统计          GET /api/project-management/statistics

29. `GET /api/recommendations/similar`
   - 位置: 第 329 行
   - 内容: 步骤3: 29.3 获取相似内容推荐      GET /api/recommendations/similar

30. `GET /api/recommendations/stats`
   - 位置: 第 333 行
   - 内容: 步骤7: 29.7 获取推荐统计          GET /api/recommendations/stats

31. `GET /api/recommendations/trending`
   - 位置: 第 328 行
   - 内容: 步骤2: 29.2 获取热门推荐          GET /api/recommendations/trending

32. `GET /api/recommendations/{id}/explanation`
   - 位置: 第 331 行
   - 内容: 步骤5: 29.5 获取推荐解释          GET /api/recommendations/{id}/explanation

33. `GET /api/resources/{id}`
   - 位置: 第 430 行
   - 内容: 步骤3: 43.3 获取资源详情          GET /api/resources/{id}

34. `POST /api/ads/impression`
   - 位置: 第 423 行
   - 内容: 步骤2: 42.2 记录广告展示          POST /api/ads/impression

35. `POST /api/analytics/export`
   - 位置: 第 397 行
   - 内容: 步骤6: 36.12 导出分析数据         POST /api/analytics/export

36. `POST /api/analytics/generate-report`
   - 位置: 第 396 行
   - 内容: 步骤5: 36.11 生成分析报告         POST /api/analytics/generate-report

37. `POST /api/audio/convert`
   - 位置: 第 384 行
   - 内容: 步骤1: 35.1 音频格式转换          POST /api/audio/convert

38. `POST /api/audio/merge`
   - 位置: 第 387 行
   - 内容: 步骤4: 35.4 音频合并              POST /api/audio/merge

39. `POST /api/audio/trim`
   - 位置: 第 386 行
   - 内容: 步骤3: 35.3 音频剪辑              POST /api/audio/trim

40. `POST /api/batch/import`
   - 位置: 第 377 行
   - 内容: 步骤3: 34.3 批量导入              POST /api/batch/import

41. `POST /api/data-export/project-data`
   - 位置: 第 340 行
   - 内容: 步骤2: 30.2 导出项目数据          POST /api/data-export/project-data

42. `POST /api/data-export/user-data`
   - 位置: 第 339 行
   - 内容: 步骤1: 30.1 导出用户数据          POST /api/data-export/user-data

43. `POST /api/downloads/create`
   - 位置: 第 364 行
   - 内容: 步骤1: 33.1 创建下载任务          POST /api/downloads/create

44. `POST /api/downloads/{id}/pause`
   - 位置: 第 366 行
   - 内容: 步骤3: 33.3 暂停下载              POST /api/downloads/{id}/pause

45. `POST /api/downloads/{id}/resume`
   - 位置: 第 367 行
   - 内容: 步骤4: 33.4 恢复下载              POST /api/downloads/{id}/resume

46. `POST /api/export/analytics`
   - 位置: 第 346 行
   - 内容: 步骤8: 31.4 导出分析数据          POST /api/export/analytics

47. `POST /api/export/batch`
   - 位置: 第 349 行
   - 内容: 步骤11: 31.7 批量导出             POST /api/export/batch

48. `POST /api/export/system-report`
   - 位置: 第 345 行
   - 内容: 步骤7: 31.3 导出系统报告          POST /api/export/system-report

49. `POST /api/export/user-stats`
   - 位置: 第 344 行
   - 内容: 步骤6: 31.2 导出用户统计          POST /api/export/user-stats

50. `POST /api/export/works`
   - 位置: 第 343 行
   - 内容: 步骤5: 31.1 导出作品数据          POST /api/export/works

51. `POST /api/general-exports/batch`
   - 位置: 第 568 行
   - 内容: 步骤7: 34.7 批量导出              POST /api/general-exports/batch

52. `POST /api/general-exports/{id}/cancel`
   - 位置: 第 566 行
   - 内容: 步骤5: 34.5 取消导出任务          POST /api/general-exports/{id}/cancel

53. `POST /api/project-management/assign-resources`
   - 位置: 第 414 行
   - 内容: 步骤3: 40.3 分配项目资源          POST /api/project-management/assign-resources

54. `POST /api/project-management/collaborate`
   - 位置: 第 416 行
   - 内容: 步骤5: 40.5 项目协作              POST /api/project-management/collaborate

55. `POST /api/project-management/tasks`
   - 位置: 第 412 行
   - 内容: 步骤1: 40.1 创建项目管理任务      POST /api/project-management/tasks

56. `POST /api/recommendations/refresh`
   - 位置: 第 334 行
   - 内容: 步骤8: 29.8 刷新推荐              POST /api/recommendations/refresh

57. `POST /api/recommendations/track-behavior`
   - 位置: 第 330 行
   - 内容: 步骤4: 29.4 记录用户行为          POST /api/recommendations/track-behavior

58. `POST /api/recommendations/{id}/feedback`
   - 位置: 第 332 行
   - 内容: 步骤6: 29.6 反馈推荐质量          POST /api/recommendations/{id}/feedback

59. `POST /api/resources/create`
   - 位置: 第 428 行
   - 内容: 步骤1: 43.1 创建资源              POST /api/resources/create

60. `PUT /api/app-monitor/alerts/{id}/acknowledge`
   - 位置: 第 281 行
   - 内容: 步骤11: 11.5 确认告警            PUT /api/app-monitor/alerts/{id}/acknowledge

61. `PUT /api/app-monitor/alerts/{id}/resolve`
   - 位置: 第 282 行
   - 内容: 步骤12: 11.6 解决告警            PUT /api/app-monitor/alerts/{id}/resolve

62. `PUT /api/batch/update`
   - 位置: 第 376 行
   - 内容: 步骤2: 34.2 批量更新              PUT /api/batch/update

## 分析结论
⚠️ **实际差异数量**: 56 个
多于预期的3个接口，可能存在其他差异。