# CogniArch 架构师战略蓝图文档

## 当前任务状态
- 任务类型：控制器业务功能重复性分析
- 执行时间：2025-07-29
- 任务描述：检查 AiGenerationController.php 和 AiTaskController.php 两个控制器的业务功能是否重复

## 战略蓝图

### 任务分析
用户询问 AiGenerationController.php 是否查询AI平台的生成任务，AiTaskController.php 是否提供给用户查询生成的任务，要求详细识别是路由冲突还是真正意义上的功能重复。

### 深度业务差异分析结果

#### 分析概览
- **分析工具**: deep_controller_analysis.py
- **分析时间**: 2025-07-29
- **分析状态**: ✅ 成功完成
- **关键发现**: 路由冲突，非真正的功能重复

#### 深度业务差异分析结果

**🎯 核心业务定位差异:**
- **AiGenerationController**: 管理与AI平台交互的实际生成任务
  - 服务类: AiGenerationService
  - 数据模型: AiGenerationTask (数据库表)
  - 业务重点: AI平台集成和任务执行
  - 数据特性: 与AI平台交互的实际任务记录

- **AiTaskController**: 为用户提供任务管理和监控界面
  - 服务类: AiTaskService
  - 数据模型: 缓存存储（模拟数据）
  - 业务重点: 用户界面和任务管理
  - 数据特性: 用于界面展示的示例数据

**🔄 路由冲突 vs 功能重复分析:**
- **发现路由冲突**: 3个 ⚠️
  - `GET /api/ai/tasks`
    - AiGenerationController.getUserTasks: 查询数据库中的真实任务
    - AiTaskController.index: 返回缓存中的模拟数据
    - **结论**: ✅ 仅路由冲突，业务逻辑不同
  - `GET /api/ai/tasks/{id}`
    - AiGenerationController.getTaskStatus: 查询数据库任务状态
    - AiTaskController.show: 返回模拟的任务详情
    - **结论**: ✅ 仅路由冲突，业务逻辑不同
  - `POST /api/ai/tasks/{id}/retry`
    - AiGenerationController.retryTask: 重试数据库中的真实任务
    - AiTaskController.retry: 创建新的缓存任务记录
    - **结论**: ✅ 仅路由冲突，业务逻辑不同

**🎭 实际使用场景差异:**
- **AiGenerationController 使用场景**:
  - AI平台集成：调用DeepSeek、KlingAI、MiniMax等平台
  - 生成任务管理：创建、监控、完成AI生成任务
  - 成本计算：记录tokens使用量和积分消耗
  - 平台切换：支持多AI平台的任务执行
  - 数据持久化：保存完整的任务执行历史

- **AiTaskController 使用场景**:
  - 用户界面：为前端提供任务展示数据
  - 任务监控：用户查看任务进度和状态
  - 统计报告：提供任务成功率和使用统计
  - 用户操作：取消任务、查看历史等
  - 快速响应：使用缓存提供快速的界面数据

**🔍 路由配置分析:**
- **在 web.php 中发现两套路由配置**:
  - 第152-157行: AiTaskController 路由
  - 第225-230行: AiGenerationController 路由
- **在 web copy.php 中**:
  - 第66-69行: AiGenerationController 路由（可能是旧版本）

**🚨 路由冲突确认:**
- 两个控制器确实使用了相同的路由路径
- 这会导致路由解析冲突，后定义的会覆盖先定义的
- 需要检查实际运行时哪个控制器在处理请求

#### 技术实现
使用Python脚本 `deep_controller_analysis.py` 进行深度业务差异分析：
- 深入分析两个控制器的真实业务逻辑
- 检查数据源和存储方式的差异
- 分析实际使用场景和业务目的
- 区分路由冲突和功能重复
- 评估控制器的职责边界
- 生成详细的业务差异分析报告

#### 格式规范示例
**添加的接口格式（符合 apitest-final.mdc 规范）:**
```markdown
#### 步骤1: 1.1 删除导出任务 DELETE /api/exports/{id}

**请求参数示例：**
```json
{
    "id": "123456",
    "force_delete": false,
    "reason": "删除原因"
}
```

**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "删除成功",
    "data": {
        "deleted_id": "123456",
        "deleted_at": "2024-01-01 12:00:00",
        "affected_count": 1
    },
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```

**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "field_name": ["字段验证失败"],
            "required_field": ["必填字段不能为空"]
        }
    },
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```
```

### 结论分析
**深度业务差异分析成功确认:**

1. **业务定位差异**:
   - ✅ 这是 **路由冲突** 而非真正的功能重复
   - ✅ 两个控制器有不同的业务目的和数据源
   - ✅ AiGenerationController: 管理真实的AI平台任务
   - ✅ AiTaskController: 提供用户界面的任务管理

2. **数据源差异**:
   - AiGenerationController: 查询数据库中的真实AI生成任务
   - AiTaskController: 使用缓存中的模拟数据为用户界面服务
   - 两者服务于完全不同的业务场景

3. **职责边界清晰**:
   - AiGenerationController: AI平台集成、成本计算、任务执行
   - AiTaskController: 用户界面、任务监控、统计报告
   - 各自有独特的业务价值

### 建议措施
1. **路由重命名**: 为两个控制器使用不同的路由前缀
   - AiGenerationController: `/api/ai/generation/*`
   - AiTaskController: `/api/ai/management/*`
2. **明确职责边界**:
   - AiGenerationController: 专注AI平台集成和任务执行
   - AiTaskController: 专注用户界面和任务管理
3. **数据整合**: 考虑让AiTaskController从AiGenerationTask读取真实数据
4. **API版本管理**: 如果需要保持兼容性，可以使用版本前缀
   - `/api/v1/ai/tasks` (AiTaskController)
   - `/api/v2/ai/tasks` (AiGenerationController)

### 最终结论
**深度分析成功**: 已成功确认这是 **路由冲突，非功能重复**。两个控制器服务于不同的业务需求，应该共存但需要解决路由冲突问题。AiGenerationController 查询的是AI平台的真实生成任务，AiTaskController 提供给用户查询和管理任务的界面服务。

## 应用规则
- 基于 Triumvirate Protocol（三体协议）开发模式
- 遵循 @.cursor/rules/ 文件夹内的规范要求

## 应用模型
- Claude Sonnet 4 by Anthropic

---

## 新任务：VersionController 接口缺失分析

### 任务背景
用户发现 apitest-final.mdc 中缺失 apitest-code.mdc 中定义的 `/api/resources/{id}/versions` 接口。

### 问题分析

#### 缺失接口识别
通过对比分析发现，apitest-code.mdc 中定义的 **VersionController (6个接口)** 在 apitest-final.mdc 中完全缺失：

1. **11.1** 创建资源版本 `POST /api/resources/{id}/versions`
2. **11.2** 获取版本历史 `GET /api/resources/{id}/versions`
3. **11.3** 获取版本详情 `GET /api/versions/{id}`
4. **11.4** 设置当前版本 `PUT /api/versions/{id}/set-current`
5. **11.5** 删除版本 `DELETE /api/versions/{id}`
6. **11.6** 版本比较 `GET /api/versions/compare`

#### 根本原因分析
1. **规划遗漏**：apitest-url.mdc 虽然提到了 "VersionController.php (6个接口)：版本控制"，但未在具体的测试阶段中列出这些接口
2. **文档不同步**：apitest-code.mdc 和 apitest-final.mdc 之间存在同步缺失
3. **功能完整性缺陷**：版本控制是资源管理的重要组成部分，缺失会影响系统完整性

### 战略规划方案

#### 方案A：补充到第七阶段资源管理系统
**位置**：第七阶段 7.16 资源管理系统之后
**理由**：版本控制是资源管理的核心功能，应与资源管理接口紧密关联

#### 方案B：创建独立的版本控制模块
**位置**：第七阶段新增 7.29 版本控制系统
**理由**：版本控制功能独立性强，可作为独立模块管理

### 推荐方案：方案A

**具体规划**：
- **位置**：在 apitest-final.mdc 第七阶段 7.16 资源管理系统（9个接口）之后
- **新增内容**：7.17 版本控制系统（6个接口）
- **测试顺序**：按照 apitest-code.mdc 中定义的 11.1-11.6 顺序

### 实施计划
1. **第一步**：在 apitest-final.mdc 中定位到资源管理系统结束位置
2. **第二步**：按照现有格式添加版本控制系统的6个接口
3. **第三步**：为每个接口补充完整的请求参数和响应示例
4. **第四步**：更新接口总数统计

### 影响评估
- **接口数量变化**：从284个增加到290个
- **功能完整性**：显著提升资源管理功能的完整性
- **测试覆盖率**：提高版本控制功能的测试覆盖

## 接管任务
@CogniDev 请接管任务，按照战略规划在 apitest-final.mdc 中补充 VersionController 的6个接口