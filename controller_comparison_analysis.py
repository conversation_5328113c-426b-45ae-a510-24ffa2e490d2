#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AiGenerationController 和 AiTaskController 业务功能重复性分析
"""

def analyze_controllers():
    """
    分析两个控制器的业务功能重复性
    """
    
    print("🔍 AiGenerationController vs AiTaskController 业务功能对比分析")
    print("=" * 80)
    
    # AiGenerationController 分析
    ai_generation_controller = {
        'name': 'AiGenerationController',
        'description': 'AI内容生成与任务管理',
        'service': 'AiGenerationService',
        'model': 'AiGenerationTask',
        'methods': {
            'generateText': {
                'route': 'POST /api/ai/text/generate',
                'purpose': '使用AI生成文本内容',
                'business_logic': '创建文本生成任务，调用AI服务生成文本',
                'data_model': 'AiGenerationTask',
                'task_types': ['text_generation']
            },
            'getTaskStatus': {
                'route': 'GET /api/ai/tasks/{id}',
                'purpose': '查询AI生成任务的状态和结果',
                'business_logic': '查询特定生成任务的详细状态信息',
                'data_model': 'AiGenerationTask',
                'task_types': ['all_generation_tasks']
            },
            'getUserTasks': {
                'route': 'GET /api/ai/tasks',
                'purpose': '获取用户的AI生成任务历史',
                'business_logic': '分页查询用户的所有生成任务',
                'data_model': 'AiGenerationTask',
                'task_types': ['all_generation_tasks']
            },
            'retryTask': {
                'route': 'POST /api/ai/tasks/{id}/retry',
                'purpose': '重新执行失败的AI生成任务',
                'business_logic': '重试失败的生成任务',
                'data_model': 'AiGenerationTask',
                'task_types': ['failed_generation_tasks']
            }
        },
        'supported_task_types': [
            'text_generation', 'image_generation', 'story_generation', 
            'character_generation', 'music_generation', 'sound_generation'
        ],
        'focus': '专注于AI内容生成任务的创建和管理'
    }
    
    # AiTaskController 分析
    ai_task_controller = {
        'name': 'AiTaskController',
        'description': 'AI任务管理控制器',
        'service': 'AiTaskService',
        'model': '缓存存储（Cache）',
        'methods': {
            'index': {
                'route': 'GET /api/ai/tasks',
                'purpose': '查询用户的AI任务列表',
                'business_logic': '分页查询用户的AI任务，支持类型和状态筛选',
                'data_model': 'Cache存储',
                'task_types': ['image', 'story', 'video', 'voice', 'music', 'sound']
            },
            'show': {
                'route': 'GET /api/ai/tasks/{id}',
                'purpose': '查询指定AI任务的详细信息',
                'business_logic': '获取特定任务的详细状态和结果',
                'data_model': 'Cache存储',
                'task_types': ['all_task_types']
            },
            'retry': {
                'route': 'POST /api/ai/tasks/{id}/retry',
                'purpose': '重新执行失败的AI任务',
                'business_logic': '重试失败任务，支持切换AI平台',
                'data_model': 'Cache存储',
                'task_types': ['failed_tasks']
            },
            'cancel': {
                'route': 'DELETE /api/ai/tasks/{id}',
                'purpose': '取消正在进行的AI任务',
                'business_logic': '取消进行中的任务并退还积分',
                'data_model': 'Cache存储',
                'task_types': ['pending_processing_tasks']
            },
            'stats': {
                'route': 'GET /api/ai/tasks/stats',
                'purpose': '获取用户的AI任务统计信息',
                'business_logic': '统计用户任务数据，按类型和状态分组',
                'data_model': 'Cache存储',
                'task_types': ['all_task_types']
            }
        },
        'supported_task_types': [
            'image', 'story', 'video', 'voice', 'music', 'sound'
        ],
        'focus': '专注于AI任务的管理、监控和统计'
    }
    
    print("📊 控制器基本信息对比:")
    print(f"   • {ai_generation_controller['name']}: {ai_generation_controller['description']}")
    print(f"     - 服务类: {ai_generation_controller['service']}")
    print(f"     - 数据模型: {ai_generation_controller['model']}")
    print(f"     - 业务重点: {ai_generation_controller['focus']}")
    print()
    print(f"   • {ai_task_controller['name']}: {ai_task_controller['description']}")
    print(f"     - 服务类: {ai_task_controller['service']}")
    print(f"     - 数据模型: {ai_task_controller['model']}")
    print(f"     - 业务重点: {ai_task_controller['focus']}")
    
    print(f"\n🔄 路由重复性分析:")
    
    # 分析路由重复
    generation_routes = {method_info['route'] for method_info in ai_generation_controller['methods'].values()}
    task_routes = {method_info['route'] for method_info in ai_task_controller['methods'].values()}
    
    duplicate_routes = generation_routes & task_routes
    
    if duplicate_routes:
        print(f"   ❌ 发现重复路由 ({len(duplicate_routes)}个):")
        for route in sorted(duplicate_routes):
            print(f"     - {route}")
            
            # 找出使用相同路由的方法
            gen_methods = [name for name, info in ai_generation_controller['methods'].items() if info['route'] == route]
            task_methods = [name for name, info in ai_task_controller['methods'].items() if info['route'] == route]
            
            print(f"       • AiGenerationController: {', '.join(gen_methods)}")
            print(f"       • AiTaskController: {', '.join(task_methods)}")
    else:
        print(f"   ✅ 无重复路由")
    
    print(f"\n📋 功能重复性分析:")
    
    # 分析功能重复
    overlapping_functions = []
    
    # 检查获取任务列表功能
    if 'GET /api/ai/tasks' in duplicate_routes:
        overlapping_functions.append({
            'function': '获取任务列表',
            'generation_method': 'getUserTasks',
            'task_method': 'index',
            'conflict_level': 'HIGH',
            'description': '两个控制器都提供获取用户AI任务列表的功能'
        })
    
    # 检查获取任务详情功能
    if 'GET /api/ai/tasks/{id}' in duplicate_routes:
        overlapping_functions.append({
            'function': '获取任务详情',
            'generation_method': 'getTaskStatus',
            'task_method': 'show',
            'conflict_level': 'HIGH',
            'description': '两个控制器都提供获取特定任务详情的功能'
        })
    
    # 检查任务重试功能
    if 'POST /api/ai/tasks/{id}/retry' in duplicate_routes:
        overlapping_functions.append({
            'function': '任务重试',
            'generation_method': 'retryTask',
            'task_method': 'retry',
            'conflict_level': 'HIGH',
            'description': '两个控制器都提供任务重试功能'
        })
    
    if overlapping_functions:
        print(f"   ❌ 发现功能重复 ({len(overlapping_functions)}个):")
        for func in overlapping_functions:
            print(f"     - {func['function']} (冲突级别: {func['conflict_level']})")
            print(f"       • {func['description']}")
            print(f"       • AiGenerationController.{func['generation_method']}")
            print(f"       • AiTaskController.{func['task_method']}")
    else:
        print(f"   ✅ 无功能重复")
    
    print(f"\n🗄️ 数据模型差异分析:")
    print(f"   • AiGenerationController 使用数据库模型 AiGenerationTask")
    print(f"     - 持久化存储，完整的任务生命周期管理")
    print(f"     - 支持复杂的查询和关联关系")
    print(f"     - 包含详细的任务元数据和执行信息")
    print()
    print(f"   • AiTaskController 使用缓存存储")
    print(f"     - 临时存储，适合快速访问")
    print(f"     - 简化的数据结构")
    print(f"     - 可能存在数据一致性问题")
    
    print(f"\n🎯 业务逻辑差异分析:")
    
    differences = [
        {
            'aspect': '任务创建',
            'generation': '直接创建并执行AI生成任务',
            'task': '创建任务但不直接执行生成逻辑'
        },
        {
            'aspect': '数据持久化',
            'generation': '使用数据库持久化存储',
            'task': '使用缓存临时存储'
        },
        {
            'aspect': '任务类型',
            'generation': '专注于内容生成任务',
            'task': '通用任务管理，支持多种类型'
        },
        {
            'aspect': '功能完整性',
            'generation': '完整的生成任务生命周期',
            'task': '任务管理和监控功能'
        },
        {
            'aspect': '扩展性',
            'generation': '易于添加新的生成类型',
            'task': '通用任务管理框架'
        }
    ]
    
    for diff in differences:
        print(f"   • {diff['aspect']}:")
        print(f"     - AiGenerationController: {diff['generation']}")
        print(f"     - AiTaskController: {diff['task']}")
    
    print(f"\n" + "=" * 80)
    print(f"🎯 结论和建议:")
    
    if duplicate_routes:
        print(f"   ❌ 存在严重的功能重复问题")
        print(f"   📋 重复程度: 高 - {len(duplicate_routes)}个重复路由")
        print(f"   🔧 建议措施:")
        print(f"     1. 统一任务管理接口，避免路由冲突")
        print(f"     2. 明确各控制器的职责边界")
        print(f"     3. 考虑合并或重构控制器")
        print(f"     4. 统一数据模型和存储方式")
    else:
        print(f"   ✅ 无明显功能重复")
        print(f"   📋 两个控制器职责相对清晰")
    
    print("=" * 80)

if __name__ == "__main__":
    analyze_controllers()
