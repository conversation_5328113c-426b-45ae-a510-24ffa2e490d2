# apitest-code.mdc 重复API接口检测报告

## 检测概览
- **总接口数量**: 279
- **重复接口类型数量**: 11
- **重复接口实例总数**: 22
- **去重后接口数量**: 268

## 重复接口详情

### 1. GET /api/ai/tasks
**重复次数**: 2

**出现位置**:
1. **第 420 行**:
   ```
   - [ ] **13.1** 获取AI任务列表 `GET /api/ai/tasks`
   ```

2. **第 750 行**:
   ```
   - [ ] **29.3** 获取用户生成任务列表 `GET /api/ai/tasks`
   ```

---

### 2. GET /api/ai/tasks/{id}
**重复次数**: 2

**出现位置**:
1. **第 424 行**:
   ```
   - [ ] **13.2** 获取AI任务详情 `GET /api/ai/tasks/{id}`
   ```

2. **第 746 行**:
   ```
   - [ ] **29.2** 获取生成任务状态 `GET /api/ai/tasks/{id}`
   ```

---

### 3. POST /api/ai/tasks/{id}/retry
**重复次数**: 2

**出现位置**:
1. **第 428 行**:
   ```
   - [ ] **13.3** 重试AI任务 `POST /api/ai/tasks/{id}/retry`
   ```

2. **第 754 行**:
   ```
   - [ ] **29.4** 重试失败的任务 `POST /api/ai/tasks/{id}/retry`
   ```

---

### 4. POST /api/batch/music/generate
**重复次数**: 2

**出现位置**:
1. **第 786 行**:
   ```
   - [ ] **31.3** 批量音乐生成 `POST /api/batch/music/generate`
   ```

2. **第 968 行**:
   ```
   - [ ] **38.5** 批量音乐生成 `POST /api/batch/music/generate`
   ```

---

### 5. GET /api/exports/list
**重复次数**: 2

**出现位置**:
1. **第 804 行**:
   ```
   - [ ] **32.2** 导出任务列表 `GET /api/exports/list`
   ```

2. **第 860 行**:
   ```
   - [ ] **34.4** 导出任务列表 `GET /api/exports/list`
   ```

---

### 6. GET /api/exports/{id}/status
**重复次数**: 2

**出现位置**:
1. **第 808 行**:
   ```
   - [ ] **32.3** 导出任务状态 `GET /api/exports/{id}/status`
   ```

2. **第 852 行**:
   ```
   - [ ] **34.2** 获取导出状态 `GET /api/exports/{id}/status`
   ```

---

### 7. GET /api/exports/{id}/download
**重复次数**: 2

**出现位置**:
1. **第 812 行**:
   ```
   - [ ] **32.4** 下载导出文件 `GET /api/exports/{id}/download`
   ```

2. **第 856 行**:
   ```
   - [ ] **34.3** 下载导出文件 `GET /api/exports/{id}/download`
   ```

---

### 8. GET /api/projects/list
**重复次数**: 2

**出现位置**:
1. **第 990 行**:
   ```
   - [ ] **39.5** 获取项目列表 `GET /api/projects/list`
   ```

2. **第 1016 行**:
   ```
   - [ ] **40.3** 获取项目列表 `GET /api/projects/list`
   ```

---

### 9. POST /api/projects/create
**重复次数**: 2

**出现位置**:
1. **第 994 行**:
   ```
   - [ ] **39.6** 创建项目 `POST /api/projects/create`
   ```

2. **第 1008 行**:
   ```
   - [ ] **40.1** 创建项目 `POST /api/projects/create`
   ```

---

### 10. PUT /api/projects/{id}
**重复次数**: 2

**出现位置**:
1. **第 998 行**:
   ```
   - [ ] **39.7** 更新项目 `PUT /api/projects/{id}`
   ```

2. **第 1024 行**:
   ```
   - [ ] **40.5** 更新项目 `PUT /api/projects/{id}`
   ```

---

### 11. DELETE /api/projects/{id}
**重复次数**: 2

**出现位置**:
1. **第 1002 行**:
   ```
   - [ ] **39.8** 删除项目 `DELETE /api/projects/{id}`
   ```

2. **第 1028 行**:
   ```
   - [ ] **40.6** 删除项目 `DELETE /api/projects/{id}`
   ```

---

## 统计分析
### 重复接口分类统计

**按HTTP方法分类**:
- DELETE: 2 个重复实例
- GET: 12 个重复实例
- POST: 6 个重复实例
- PUT: 2 个重复实例

**重复最多的路径**:
- /api/projects/{id}: 4 次重复
- /api/ai/tasks: 2 次重复
- /api/ai/tasks/{id}: 2 次重复
- /api/ai/tasks/{id}/retry: 2 次重复
- /api/batch/music/generate: 2 次重复

## 建议
发现重复的API接口定义，建议：
1. **立即清理**: 删除重复的接口定义，保留功能最完整的版本
2. **统一管理**: 建立接口定义的统一管理机制
3. **自动检测**: 在文档更新流程中加入重复检测步骤
4. **版本控制**: 使用版本控制跟踪接口变更
5. **文档整合**: 合并相关功能的接口定义