# apitest-final.mdc vs apitest-code.mdc API接口差异对比报告

## 统计概览
- **apitest-final.mdc**: 275 个接口（去重后）
- **apitest-code.mdc**: 268 个接口（去重后）
- **共同接口**: 218 个
- **仅在 final 中**: 57 个
- **仅在 code 中**: 50 个
- **差异总数**: 107 个

## 仅在 apitest-final.mdc 中的接口

### 1. DELETE /api/batch/delete
- **位置**: 第 11413 行
- **内容**: #### 步骤1: 34.1 批量删除 DELETE /api/batch/delete

### 2. DELETE /api/batch/{id}
- **位置**: 第 11798 行
- **内容**: #### 步骤5: 34.5 取消批量操作 DELETE /api/batch/{id}

### 3. DELETE /api/export/{id}
- **位置**: 第 11012 行
- **内容**: #### 步骤10: 31.6 取消导出任务 DELETE /api/export/{id}

### 4. DELETE /api/general-exports/{id}
- **位置**: 第 17094 行
- **内容**: #### 步骤6: 34.6 删除导出任务 DELETE /api/general-exports/{id}

### 5. DELETE /api/logs/cleanup
- **位置**: 第 12723 行
- **内容**: #### 步骤5: 37.5 清理日志 DELETE /api/logs/cleanup

### 6. GET /api/ads/config
- **位置**: 第 13111 行
- **内容**: #### 步骤1: 42.1 获取广告配置 GET /api/ads/config

### 7. GET /api/analytics/content
- **位置**: 第 12536 行
- **内容**: #### 步骤4: 36.10 获取内容分析 GET /api/analytics/content

### 8. GET /api/analytics/performance
- **位置**: 第 12483 行
- **内容**: #### 步骤2: 36.8 获取性能分析 GET /api/analytics/performance

### 9. GET /api/analytics/usage
- **位置**: 第 12338 行
- **内容**: #### 步骤1: 36.7 获取使用分析 GET /api/analytics/usage

### 10. GET /api/app-monitor/alerts
- **位置**: 第 8760 行
- **内容**: #### 步骤10: 11.4 应用告警列表 GET /api/app-monitor/alerts

### 11. GET /api/app-monitor/metrics
- **位置**: 第 8726 行
- **内容**: #### 步骤8: 11.2 应用性能指标 GET /api/app-monitor/metrics

### 12. GET /api/app-monitor/realtime
- **位置**: 第 8743 行
- **内容**: #### 步骤9: 11.3 实时监控数据 GET /api/app-monitor/realtime

### 13. GET /api/batch/{id}/status
- **位置**: 第 11711 行
- **内容**: #### 步骤4: 34.4 获取批量操作状态 GET /api/batch/{id}/status

### 14. GET /api/data-export/{id}/download
- **位置**: 第 10826 行
- **内容**: #### 步骤4: 30.4 下载导出文件 GET /api/data-export/{id}/download

### 15. GET /api/data-export/{id}/status
- **位置**: 第 10778 行
- **内容**: #### 步骤3: 30.3 获取导出状态 GET /api/data-export/{id}/status

### 16. GET /api/export/history
- **位置**: 第 10985 行
- **内容**: #### 步骤9: 31.5 获取导出历史 GET /api/export/history

### 17. GET /api/files/{id}/info
- **位置**: 第 11137 行
- **内容**: #### 步骤2: 32.2 获取文件信息 GET /api/files/{id}/info

### 18. GET /api/files/{id}/preview
- **位置**: 第 11295 行
- **内容**: #### 步骤5: 32.5 文件预览 GET /api/files/{id}/preview

### 19. GET /api/general-exports/list
- **位置**: 第 17049 行
- **内容**: #### 步骤4: 34.4 导出任务列表 GET /api/general-exports/list

### 20. GET /api/general-exports/{id}/download
- **位置**: 第 17026 行
- **内容**: #### 步骤3: 34.3 下载导出文件 GET /api/general-exports/{id}/download

### 21. GET /api/general-exports/{id}/status
- **位置**: 第 17002 行
- **内容**: #### 步骤2: 34.2 获取导出状态 GET /api/general-exports/{id}/status

### 22. GET /api/logs/api-calls
- **位置**: 第 12698 行
- **内容**: #### 步骤4: 37.4 获取API调用日志 GET /api/logs/api-calls

### 23. GET /api/project-management/milestones
- **位置**: 第 13072 行
- **内容**: #### 步骤6: 40.6 项目里程碑 GET /api/project-management/milestones

### 24. GET /api/project-management/progress
- **位置**: 第 12854 行
- **内容**: #### 步骤2: 40.2 获取项目进度 GET /api/project-management/progress

### 25. GET /api/project-management/statistics
- **位置**: 第 13023 行
- **内容**: #### 步骤4: 40.4 获取项目统计 GET /api/project-management/statistics

### 26. GET /api/recommendations/similar
- **位置**: 第 10199 行
- **内容**: #### 步骤3: 29.3 获取相似内容推荐 GET /api/recommendations/similar

### 27. GET /api/recommendations/stats
- **位置**: 第 10465 行
- **内容**: #### 步骤7: 29.7 获取推荐统计 GET /api/recommendations/stats

### 28. GET /api/recommendations/trending
- **位置**: 第 10156 行
- **内容**: #### 步骤2: 29.2 获取热门推荐 GET /api/recommendations/trending

### 29. GET /api/recommendations/{id}/explanation
- **位置**: 第 10290 行
- **内容**: #### 步骤5: 29.5 获取推荐解释 GET /api/recommendations/{id}/explanation

### 30. GET /api/resources/{id}
- **位置**: 第 13300 行
- **内容**: #### 步骤3: 43.3 获取资源详情 GET /api/resources/{id}

### 31. POST /api/ads/impression
- **位置**: 第 13169 行
- **内容**: #### 步骤2: 42.2 记录广告展示 POST /api/ads/impression

### 32. POST /api/analytics/export
- **位置**: 第 12578 行
- **内容**: #### 步骤6: 36.12 导出分析数据 POST /api/analytics/export

### 33. POST /api/analytics/generate-report
- **位置**: 第 12559 行
- **内容**: #### 步骤5: 36.11 生成分析报告 POST /api/analytics/generate-report

### 34. POST /api/audio/convert
- **位置**: 第 11893 行
- **内容**: #### 步骤1: 35.1 音频格式转换 POST /api/audio/convert

### 35. POST /api/audio/merge
- **位置**: 第 12228 行
- **内容**: #### 步骤4: 35.4 音频合并 POST /api/audio/merge

### 36. POST /api/audio/trim
- **位置**: 第 12117 行
- **内容**: #### 步骤3: 35.3 音频剪辑 POST /api/audio/trim

### 37. POST /api/batch/import
- **位置**: 第 11627 行
- **内容**: #### 步骤3: 34.3 批量导入 POST /api/batch/import

### 38. POST /api/batch/texts/process
- **位置**: 第 16334 行
- **内容**: #### 步骤3: 31.3 批量文本处理 POST /api/batch/texts/process

### 39. POST /api/data-export/project-data
- **位置**: 第 10715 行
- **内容**: #### 步骤2: 30.2 导出项目数据 POST /api/data-export/project-data

### 40. POST /api/data-export/user-data
- **位置**: 第 10610 行
- **内容**: #### 步骤1: 30.1 导出用户数据 POST /api/data-export/user-data

### 41. POST /api/export/analytics
- **位置**: 第 10967 行
- **内容**: #### 步骤8: 31.4 导出分析数据 POST /api/export/analytics

### 42. POST /api/export/batch
- **位置**: 第 11028 行
- **内容**: #### 步骤11: 31.7 批量导出 POST /api/export/batch

### 43. POST /api/export/system-report
- **位置**: 第 10934 行
- **内容**: #### 步骤7: 31.3 导出系统报告 POST /api/export/system-report

### 44. POST /api/export/user-stats
- **位置**: 第 10892 行
- **内容**: #### 步骤6: 31.2 导出用户统计 POST /api/export/user-stats

### 45. POST /api/export/works
- **位置**: 第 10856 行
- **内容**: #### 步骤5: 31.1 导出作品数据 POST /api/export/works

### 46. POST /api/general-exports/batch
- **位置**: 第 17111 行
- **内容**: #### 步骤7: 34.7 批量导出 POST /api/general-exports/batch

### 47. POST /api/general-exports/{id}/cancel
- **位置**: 第 17075 行
- **内容**: #### 步骤5: 34.5 取消导出任务 POST /api/general-exports/{id}/cancel

### 48. POST /api/project-management/assign-resources
- **位置**: 第 12926 行
- **内容**: #### 步骤3: 40.3 分配项目资源 POST /api/project-management/assign-resources

### 49. POST /api/project-management/collaborate
- **位置**: 第 13052 行
- **内容**: #### 步骤5: 40.5 项目协作 POST /api/project-management/collaborate

### 50. POST /api/project-management/tasks
- **位置**: 第 12763 行
- **内容**: #### 步骤1: 40.1 创建项目管理任务 POST /api/project-management/tasks

### 51. POST /api/recommendations/refresh
- **位置**: 第 10541 行
- **内容**: #### 步骤8: 29.8 刷新推荐 POST /api/recommendations/refresh

### 52. POST /api/recommendations/track-behavior
- **位置**: 第 10248 行
- **内容**: #### 步骤4: 29.4 记录用户行为 POST /api/recommendations/track-behavior

### 53. POST /api/recommendations/{id}/feedback
- **位置**: 第 10369 行
- **内容**: #### 步骤6: 29.6 反馈推荐质量 POST /api/recommendations/{id}/feedback

### 54. POST /api/resources/create
- **位置**: 第 13233 行
- **内容**: #### 步骤1: 43.1 创建资源 POST /api/resources/create

### 55. PUT /api/app-monitor/alerts/{id}/acknowledge
- **位置**: 第 8785 行
- **内容**: #### 步骤11: 11.5 确认告警 PUT /api/app-monitor/alerts/{id}/acknowledge

### 56. PUT /api/app-monitor/alerts/{id}/resolve
- **位置**: 第 8803 行
- **内容**: #### 步骤12: 11.6 解决告警 PUT /api/app-monitor/alerts/{id}/resolve

### 57. PUT /api/batch/update
- **位置**: 第 11518 行
- **内容**: #### 步骤2: 34.2 批量更新 PUT /api/batch/update

## 仅在 apitest-code.mdc 中的接口

### 1. DELETE /api/exports/{id}
- **位置**: 第 696 行
- **内容**: - [ ] **34.6** 删除导出任务 `DELETE /api/exports/{id}`

### 2. DELETE /api/projects/{id}
- **位置**: 第 830 行
- **内容**: - [ ] **39.8** 删除项目 `DELETE /api/projects/{id}`

### 3. DELETE /api/publications/{id}
- **位置**: 第 874 行
- **内容**: - [ ] **41.4** 取消发布 `DELETE /api/publications/{id}`

### 4. DELETE /api/works/{id}
- **位置**: 第 1130 行
- **内容**: - [ ] **49.3** 删除作品 `DELETE /api/works/{id}`

### 5. GET /api/ai-models/usage-stats
- **位置**: 第 250 行
- **内容**: - [ ] **12.2** 获取使用统计 `GET /api/ai-models/usage-stats`

### 6. GET /api/analytics/ai-performance
- **位置**: 第 294 行
- **内容**: - [ ] **14.3** 获取AI平台性能分析 `GET /api/analytics/ai-performance`

### 7. GET /api/analytics/revenue
- **位置**: 第 302 行
- **内容**: - [ ] **14.5** 获取收入分析 `GET /api/analytics/revenue`

### 8. GET /api/analytics/system-usage
- **位置**: 第 290 行
- **内容**: - [ ] **14.2** 获取系统使用统计 `GET /api/analytics/system-usage`

### 9. GET /api/analytics/user-retention
- **位置**: 第 298 行
- **内容**: - [ ] **14.4** 获取用户留存分析 `GET /api/analytics/user-retention`

### 10. GET /api/files/{id}
- **位置**: 第 714 行
- **内容**: - [ ] **35.3** 文件详情 `GET /api/files/{id}`

### 11. GET /api/images/history
- **位置**: 第 748 行
- **内容**: - [ ] **36.6** 图像生成历史 `GET /api/images/history`

### 12. GET /api/logs/ai-calls
- **位置**: 第 762 行
- **内容**: - [ ] **37.3** 查询AI调用日志 `GET /api/logs/ai-calls`

### 13. GET /api/monitor/alerts
- **位置**: 第 202 行
- **内容**: - [ ] **10.4** 系统告警列表 `GET /api/monitor/alerts`

### 14. GET /api/monitor/metrics
- **位置**: 第 194 行
- **内容**: - [ ] **10.2** 系统性能指标 `GET /api/monitor/metrics`

### 15. GET /api/monitor/realtime
- **位置**: 第 198 行
- **内容**: - [ ] **10.3** 实时监控数据 `GET /api/monitor/realtime`

### 16. GET /api/projects/list
- **位置**: 第 818 行
- **内容**: - [ ] **39.5** 获取项目列表 `GET /api/projects/list`

### 17. GET /api/projects/{id}
- **位置**: 第 814 行
- **内容**: - [ ] **39.4** 获取项目详情 `GET /api/projects/{id}`

### 18. GET /api/publications/{id}/status
- **位置**: 第 866 行
- **内容**: - [ ] **41.2** 获取发布状态 `GET /api/publications/{id}/status`

### 19. GET /api/recommendations/analytics
- **位置**: 第 924 行
- **内容**: - [ ] **42.7** 获取推荐统计 `GET /api/recommendations/analytics`

### 20. GET /api/recommendations/content
- **位置**: 第 900 行
- **内容**: - [ ] **42.1** 获取内容推荐 `GET /api/recommendations/content`

### 21. GET /api/recommendations/preferences
- **位置**: 第 916 行
- **内容**: - [ ] **42.5** 获取推荐设置 `GET /api/recommendations/preferences`

### 22. GET /api/recommendations/topics
- **位置**: 第 908 行
- **内容**: - [ ] **42.3** 获取话题推荐 `GET /api/recommendations/topics`

### 23. GET /api/recommendations/users
- **位置**: 第 904 行
- **内容**: - [ ] **42.2** 获取用户推荐 `GET /api/recommendations/users`

### 24. GET /api/resources/{id}/status
- **位置**: 第 938 行
- **内容**: - [ ] **43.2** 资源生成状态查询 `GET /api/resources/{id}/status`

### 25. GET /api/videos/platform-comparison
- **位置**: 第 1070 行
- **内容**: - [ ] **47.3** 视频平台性能对比 `GET /api/videos/platform-comparison`

### 26. GET /api/websocket/sessions
- **位置**: 第 126 行
- **内容**: - [ ] **7.2** 获取WebSocket会话列表 `GET /api/websocket/sessions`

### 27. GET /api/works/trending
- **位置**: 第 1150 行
- **内容**: - [ ] **49.8** 热门作品 `GET /api/works/trending`

### 28. GET /api/works/{id}/share
- **位置**: 第 1142 行
- **内容**: - [ ] **49.6** 获取分享链接 `GET /api/works/{id}/share`

### 29. POST /api/ai-models/{model_id}/favorite
- **位置**: 第 254 行
- **内容**: - [ ] **12.3** 收藏模型 `POST /api/ai-models/{model_id}/favorite`

### 30. POST /api/ai-models/{model_id}/test
- **位置**: 第 246 行
- **内容**: - [ ] **12.1** 测试模型 `POST /api/ai-models/{model_id}/test`

### 31. POST /api/ai/text/generate
- **位置**: 第 570 行
- **内容**: - [ ] **29.1** 文本生成 `POST /api/ai/text/generate`

### 32. POST /api/analytics/custom-report
- **位置**: 第 306 行
- **内容**: - [ ] **14.6** 生成自定义报告 `POST /api/analytics/custom-report`

### 33. POST /api/exports/batch
- **位置**: 第 700 行
- **内容**: - [ ] **34.7** 批量导出 `POST /api/exports/batch`

### 34. POST /api/exports/{id}/cancel
- **位置**: 第 692 行
- **内容**: - [ ] **34.5** 取消导出任务 `POST /api/exports/{id}/cancel`

### 35. POST /api/images/batch-generate
- **位置**: 第 740 行
- **内容**: - [ ] **36.4** 批量图像生成 `POST /api/images/batch-generate`

### 36. POST /api/images/{task_id}/switch-platform
- **位置**: 第 736 行
- **内容**: - [ ] **36.3** 平台切换 `POST /api/images/{task_id}/switch-platform`

### 37. POST /api/projects/create
- **位置**: 第 822 行
- **内容**: - [ ] **39.6** 创建项目 `POST /api/projects/create`

### 38. POST /api/projects/{id}/collaboration
- **位置**: 第 840 行
- **内容**: - [ ] **40.2** 项目协作管理 `POST /api/projects/{id}/collaboration`

### 39. POST /api/publications/{id}/unpublish
- **位置**: 第 878 行
- **内容**: - [ ] **41.5** 取消发布(POST方式) `POST /api/publications/{id}/unpublish`

### 40. POST /api/recommendations/feedback
- **位置**: 第 912 行
- **内容**: - [ ] **42.4** 反馈推荐 `POST /api/recommendations/feedback`

### 41. POST /api/resources/generate
- **位置**: 第 934 行
- **内容**: - [ ] **43.1** 资源生成任务创建 `POST /api/resources/generate`

### 42. POST /api/websocket/disconnect
- **位置**: 第 130 行
- **内容**: - [ ] **7.3** 断开WebSocket连接 `POST /api/websocket/disconnect`

### 43. POST /api/works/{id}/like
- **位置**: 第 1146 行
- **内容**: - [ ] **49.7** 点赞作品 `POST /api/works/{id}/like`

### 44. PUT /api/logs/errors/{id}/resolve
- **位置**: 第 770 行
- **内容**: - [ ] **37.5** 标记错误为已解决 `PUT /api/logs/errors/{id}/resolve`

### 45. PUT /api/monitor/alerts/{id}/acknowledge
- **位置**: 第 206 行
- **内容**: - [ ] **10.5** 确认告警 `PUT /api/monitor/alerts/{id}/acknowledge`

### 46. PUT /api/monitor/alerts/{id}/resolve
- **位置**: 第 210 行
- **内容**: - [ ] **10.6** 解决告警 `PUT /api/monitor/alerts/{id}/resolve`

### 47. PUT /api/projects/{id}
- **位置**: 第 826 行
- **内容**: - [ ] **39.7** 更新项目 `PUT /api/projects/{id}`

### 48. PUT /api/publications/{id}
- **位置**: 第 870 行
- **内容**: - [ ] **41.3** 更新作品信息 `PUT /api/publications/{id}`

### 49. PUT /api/recommendations/preferences
- **位置**: 第 920 行
- **内容**: - [ ] **42.6** 更新推荐设置 `PUT /api/recommendations/preferences`

### 50. PUT /api/works/{id}
- **位置**: 第 1126 行
- **内容**: - [ ] **49.2** 编辑作品 `PUT /api/works/{id}`

## 差异分类统计
### apitest-final.mdc 独有接口分类
- DELETE: 5 个
- GET: 25 个
- POST: 24 个
- PUT: 3 个

### apitest-code.mdc 独有接口分类
- DELETE: 4 个
- GET: 24 个
- POST: 15 个
- PUT: 7 个

## 分析结论
✅ **apitest-final.mdc 比 apitest-code.mdc 多 7 个接口**

## 建议
发现接口差异，建议：
1. **同步检查**: 确认差异接口是否为合理的版本差异
2. **功能验证**: 验证独有接口的功能完整性
3. **文档统一**: 考虑是否需要统一两个文档的接口定义
4. **版本管理**: 建立清晰的文档版本管理机制