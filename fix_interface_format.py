#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正接口格式工具
按照 apitest-code.mdc 的正确格式添加57个接口
包括按控制器分类和完整的请求参数、成功响应、错误响应
"""

import re
from typing import List, Dict, Set, Tuple
from collections import defaultdict

def extract_api_interfaces_from_final_file(file_path: str) -> List[Tuple[str, int, str]]:
    """
    从 apitest-final.mdc 文件中提取API接口
    返回: [(接口定义, 行号, 完整行内容), ...]
    """
    interfaces = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 匹配格式：空格+http协议+空格+api接口地址
        pattern = r' (GET|POST|PUT|DELETE|PATCH) (/api/[^\s]+)'
        
        for line_num, line in enumerate(lines, 1):
            match = re.search(pattern, line)
            if match:
                method = match.group(1)
                path = match.group(2)
                interface_key = f"{method} {path}"
                interfaces.append((interface_key, line_num, line.strip()))
                
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
        return []
    except Exception as e:
        print(f"错误：读取文件时发生异常 - {e}")
        return []
    
    return interfaces

def extract_api_interfaces_from_code_file(file_path: str) -> List[Tuple[str, int, str]]:
    """
    从 apitest-code.mdc 文件中提取API接口
    返回: [(接口定义, 行号, 完整行内容), ...]
    """
    interfaces = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 匹配格式：- [ ] **X.Y** 接口名称 `HTTP方法 /api/路径`
        pattern = r'- \[ \] \*\*\d+\.\d+\*\* .* `(GET|POST|PUT|DELETE|PATCH) (/api/[^`]+)`'
        
        for line_num, line in enumerate(lines, 1):
            match = re.search(pattern, line)
            if match:
                method = match.group(1)
                path = match.group(2)
                interface_key = f"{method} {path}"
                interfaces.append((interface_key, line_num, line.strip()))
                
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
        return []
    except Exception as e:
        print(f"错误：读取文件时发生异常 - {e}")
        return []
    
    return interfaces

def find_unique_interfaces(final_interfaces: List[Tuple[str, int, str]], 
                          code_interfaces: List[Tuple[str, int, str]]) -> List[Tuple[str, int, str]]:
    """
    找出 apitest-final.mdc 独有的接口
    """
    # 转换为集合进行差异计算，去除重复
    final_set = {interface[0] for interface in final_interfaces}
    code_set = {interface[0] for interface in code_interfaces}
    
    only_in_final_keys = final_set - code_set
    
    # 获取详细信息，每个接口只保留第一次出现的位置
    final_dict = {}
    for key, line, content in final_interfaces:
        if key not in final_dict:
            final_dict[key] = (line, content)
    
    only_in_final = [(key, final_dict[key][0], final_dict[key][1]) for key in only_in_final_keys]
    
    return sorted(only_in_final)

def classify_interfaces_by_controller(interfaces: List[Tuple[str, int, str]]) -> Dict[str, List[Tuple[str, str, str]]]:
    """
    按控制器分类接口
    返回: {控制器名: [(接口定义, 接口名称, 原始内容), ...]}
    """
    controllers = defaultdict(list)
    
    # 定义路径到控制器的映射
    path_to_controller = {
        '/api/ads/': 'AdsController',
        '/api/analytics/': 'AnalyticsController', 
        '/api/app-monitor/': 'AppMonitorController',
        '/api/audio/': 'AudioController',
        '/api/batch/': 'BatchController',
        '/api/data-export/': 'DataExportController',
        '/api/export/': 'ExportController',
        '/api/files/': 'FilesController',
        '/api/general-exports/': 'GeneralExportsController',
        '/api/logs/': 'LogsController',
        '/api/project-management/': 'ProjectManagementController',
        '/api/recommendations/': 'RecommendationsController',
        '/api/resources/': 'ResourcesController',
    }
    
    for interface_key, line_num, original_content in interfaces:
        # 从原始内容中提取接口名称
        pattern = r'#### 步骤\d+: \d+\.\d+ (.+?) (GET|POST|PUT|DELETE|PATCH) (/api/[^\s]+)'
        match = re.search(pattern, original_content)
        
        if match:
            interface_name = match.group(1)
            method = match.group(2)
            path = match.group(3)
            
            # 确定控制器
            controller_name = 'OtherController'  # 默认控制器
            for path_prefix, controller in path_to_controller.items():
                if path.startswith(path_prefix):
                    controller_name = controller
                    break
            
            controllers[controller_name].append((interface_key, interface_name, original_content))
    
    return controllers

def generate_interface_content(interface_key: str, interface_name: str, number: str) -> List[str]:
    """
    生成符合规范的接口内容
    """
    method, path = interface_key.split(' ', 1)
    
    # 根据接口类型生成合适的参数和响应
    content = []
    content.append(f"- [ ] **{number}** {interface_name} `{interface_key}`")
    
    # 生成请求参数
    if method == 'GET':
        if '{id}' in path:
            content.append(f"  - 请求参数：`id` (路径参数), `include_details`")
        else:
            content.append(f"  - 请求参数：`page, per_page, search, filters`")
    elif method == 'POST':
        if 'export' in path.lower():
            content.append(f"  - 请求参数：`export_type, format, date_range, filters`")
        elif 'batch' in path.lower():
            content.append(f"  - 请求参数：`items, operation_type, options`")
        else:
            content.append(f"  - 请求参数：`data, options, metadata`")
    elif method == 'PUT':
        content.append(f"  - 请求参数：`id` (路径参数), `update_data, options`")
    elif method == 'DELETE':
        if '{id}' in path:
            content.append(f"  - 请求参数：`id` (路径参数), `force_delete`")
        else:
            content.append(f"  - 请求参数：`criteria, confirm`")
    
    # 生成成功响应
    if method == 'GET':
        content.append(f"  - 成功响应：`200` - 数据获取成功")
    elif method == 'POST':
        content.append(f"  - 成功响应：`201` - 创建成功, `200` - 操作成功")
    elif method == 'PUT':
        content.append(f"  - 成功响应：`200` - 更新成功")
    elif method == 'DELETE':
        content.append(f"  - 成功响应：`200` - 删除成功")
    
    # 生成错误响应
    error_responses = []
    if '{id}' in path:
        error_responses.append("`404` - 资源不存在")
    if method in ['POST', 'PUT']:
        error_responses.append("`422` - 参数验证失败")
    error_responses.append("`401` - 未登录")
    if method == 'DELETE':
        error_responses.append("`403` - 无删除权限")
    
    content.append(f"  - 错误响应：{', '.join(error_responses)}")
    
    return content

def add_formatted_interfaces_to_code_file(code_file_path: str, controllers: Dict[str, List[Tuple[str, str, str]]]):
    """
    将格式化的接口添加到 apitest-code.mdc 文件中
    """
    try:
        # 读取原文件内容
        with open(code_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 准备要添加的内容
        new_content = []
        new_content.append("\n")
        new_content.append("---\n")
        new_content.append("## 🟡 **从 apitest-final.mdc 同步的新增接口**\n")
        new_content.append("\n")
        
        interface_counter = 1
        
        # 按控制器添加接口
        for controller_name in sorted(controllers.keys()):
            interfaces = controllers[controller_name]
            new_content.append(f"### **{controller_name} ({len(interfaces)}个接口)**\n")
            
            for interface_key, interface_name, original_content in interfaces:
                number = f"52.{interface_counter}"
                interface_lines = generate_interface_content(interface_key, interface_name, number)
                
                for line in interface_lines:
                    new_content.append(line + "\n")
                
                interface_counter += 1
            
            new_content.append("\n")
        
        # 将新内容添加到文件末尾
        lines.extend(new_content)
        
        # 写回文件
        with open(code_file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)
            
        print(f"✅ 成功添加 {interface_counter - 1} 个格式化接口到 {code_file_path}")
        
    except Exception as e:
        print(f"❌ 添加接口时发生错误: {e}")

def main():
    """主函数"""
    final_file = "apitest-final.mdc"
    code_file = "apitest-code.mdc"
    
    print("🔧 开始修正接口格式...")
    print(f"📄 源文件: {final_file}")
    print(f"📄 目标文件: {code_file}")
    print()
    
    # 提取两个文件中的API接口
    final_interfaces = extract_api_interfaces_from_final_file(final_file)
    code_interfaces = extract_api_interfaces_from_code_file(code_file)
    
    if not final_interfaces:
        print("❌ 未找到源文件中的API接口定义")
        return
    
    if not code_interfaces:
        print("❌ 未找到目标文件中的API接口定义")
        return
    
    # 找出独有接口
    unique_interfaces = find_unique_interfaces(final_interfaces, code_interfaces)
    
    print(f"📊 找到 {len(unique_interfaces)} 个独有接口需要格式化添加")
    
    if not unique_interfaces:
        print("✅ 没有需要添加的接口")
        return
    
    # 按控制器分类
    controllers = classify_interfaces_by_controller(unique_interfaces)
    
    print(f"📁 按 {len(controllers)} 个控制器分类完成")
    for controller, interfaces in controllers.items():
        print(f"   • {controller}: {len(interfaces)} 个接口")
    
    # 添加格式化接口到目标文件
    add_formatted_interfaces_to_code_file(code_file, controllers)
    
    print()
    print("=" * 70)
    print("🎉 接口格式修正完成!")
    print(f"✅ 成功添加 {len(unique_interfaces)} 个规范格式的接口")
    print("📋 包含完整的请求参数、成功响应和错误响应")
    print("📁 按控制器正确分类")
    print("=" * 70)

if __name__ == "__main__":
    main()
