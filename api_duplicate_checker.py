#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API接口重复检测工具
检测 apitest-final.mdc 中重复的API接口
规则：空格+协议类型+空格+api接口
"""

import re
from collections import defaultdict, Counter
from typing import List, Dict, Tuple

def extract_api_interfaces(file_path: str) -> List[Tuple[str, int, str]]:
    """
    从文件中提取所有API接口定义
    返回: [(接口定义, 行号, 完整行内容), ...]
    """
    interfaces = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 正则表达式匹配接口定义
        pattern = r'#### 步骤\d+: \d+\.\d+ .* (GET|POST|PUT|DELETE|PATCH) (/api/[^\s]+)'
        
        for line_num, line in enumerate(lines, 1):
            match = re.search(pattern, line)
            if match:
                method = match.group(1)
                path = match.group(2)
                interface_key = f"{method} {path}"
                interfaces.append((interface_key, line_num, line.strip()))
                
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
        return []
    except Exception as e:
        print(f"错误：读取文件时发生异常 - {e}")
        return []
    
    return interfaces

def find_duplicates(interfaces: List[Tuple[str, int, str]]) -> Dict[str, List[Tuple[int, str]]]:
    """
    查找重复的API接口
    返回: {接口定义: [(行号, 完整行内容), ...], ...}
    """
    interface_map = defaultdict(list)
    
    for interface_key, line_num, full_line in interfaces:
        interface_map[interface_key].append((line_num, full_line))
    
    # 只返回出现次数大于1的接口
    duplicates = {k: v for k, v in interface_map.items() if len(v) > 1}
    
    return duplicates

def generate_report(duplicates: Dict[str, List[Tuple[int, str]]], total_interfaces: int) -> str:
    """
    生成重复接口检测报告
    """
    report = []
    report.append("# API接口重复检测报告")
    report.append("")
    report.append(f"## 检测概览")
    report.append(f"- 总接口数量: {total_interfaces}")
    report.append(f"- 重复接口类型数量: {len(duplicates)}")
    
    total_duplicate_instances = sum(len(instances) for instances in duplicates.values())
    report.append(f"- 重复接口实例总数: {total_duplicate_instances}")
    report.append("")
    
    if not duplicates:
        report.append("## 检测结果")
        report.append("✅ **未发现重复的API接口**")
        report.append("")
        report.append("所有API接口都是唯一的，没有重复定义。")
    else:
        report.append("## 重复接口详情")
        report.append("")
        
        for i, (interface_key, instances) in enumerate(duplicates.items(), 1):
            report.append(f"### {i}. {interface_key}")
            report.append(f"**重复次数**: {len(instances)}")
            report.append("")
            report.append("**出现位置**:")
            
            for j, (line_num, full_line) in enumerate(instances, 1):
                report.append(f"{j}. 第 {line_num} 行:")
                report.append(f"   ```")
                report.append(f"   {full_line}")
                report.append(f"   ```")
                report.append("")
            
            report.append("---")
            report.append("")
    
    report.append("## 建议")
    if duplicates:
        report.append("发现重复的API接口定义，建议：")
        report.append("1. 检查重复接口是否为误重复定义")
        report.append("2. 确认是否需要合并或删除重复的接口")
        report.append("3. 更新相关文档以保持一致性")
    else:
        report.append("所有API接口定义都是唯一的，文档结构良好。")
    
    return "\n".join(report)

def main():
    """主函数"""
    file_path = "apitest-final.mdc"
    
    print("🔍 开始检测API接口重复...")
    print(f"📄 分析文件: {file_path}")
    print()
    
    # 提取所有API接口
    interfaces = extract_api_interfaces(file_path)
    
    if not interfaces:
        print("❌ 未找到任何API接口定义")
        return
    
    print(f"📊 找到 {len(interfaces)} 个API接口定义")
    
    # 查找重复接口
    duplicates = find_duplicates(interfaces)
    
    # 生成报告
    report = generate_report(duplicates, len(interfaces))
    
    # 保存报告到文件
    report_file = "api_duplicate_report.md"
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"📝 报告已保存到: {report_file}")
    except Exception as e:
        print(f"❌ 保存报告失败: {e}")
    
    # 输出简要结果
    print()
    print("=" * 50)
    if duplicates:
        print(f"⚠️  发现 {len(duplicates)} 种重复的API接口")
        for interface_key, instances in duplicates.items():
            print(f"   • {interface_key} (重复 {len(instances)} 次)")
    else:
        print("✅ 未发现重复的API接口")
    print("=" * 50)

if __name__ == "__main__":
    main()
