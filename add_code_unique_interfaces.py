#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将 apitest-code.mdc 独有的50个接口按照 apitest-final.mdc 格式添加
包括完整的请求参数和业务状态码响应格式化数据示例代码
"""

import re
from typing import List, Dict, Set, Tuple
from collections import defaultdict

def extract_api_interfaces_from_final_file(file_path: str) -> List[Tuple[str, int, str]]:
    """
    从 apitest-final.mdc 文件中提取API接口
    返回: [(接口定义, 行号, 完整行内容), ...]
    """
    interfaces = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 匹配格式：空格+http协议+空格+api接口地址
        pattern = r' (GET|POST|PUT|DELETE|PATCH) (/api/[^\s]+)'
        
        for line_num, line in enumerate(lines, 1):
            match = re.search(pattern, line)
            if match:
                method = match.group(1)
                path = match.group(2)
                interface_key = f"{method} {path}"
                interfaces.append((interface_key, line_num, line.strip()))
                
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
        return []
    except Exception as e:
        print(f"错误：读取文件时发生异常 - {e}")
        return []
    
    return interfaces

def extract_api_interfaces_from_code_file(file_path: str) -> List[Tuple[str, int, str]]:
    """
    从 apitest-code.mdc 文件中提取API接口
    返回: [(接口定义, 行号, 完整行内容), ...]
    """
    interfaces = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 匹配格式：- [ ] **X.Y** 接口名称 `HTTP方法 /api/路径`
        pattern = r'- \[ \] \*\*\d+\.\d+\*\* (.+?) `(GET|POST|PUT|DELETE|PATCH) (/api/[^`]+)`'
        
        for line_num, line in enumerate(lines, 1):
            match = re.search(pattern, line)
            if match:
                interface_name = match.group(1)
                method = match.group(2)
                path = match.group(3)
                interface_key = f"{method} {path}"
                interfaces.append((interface_key, line_num, line.strip(), interface_name))
                
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
        return []
    except Exception as e:
        print(f"错误：读取文件时发生异常 - {e}")
        return []
    
    return interfaces

def find_code_unique_interfaces(final_interfaces: List[Tuple[str, int, str]], 
                               code_interfaces: List[Tuple[str, int, str, str]]) -> List[Tuple[str, int, str, str]]:
    """
    找出 apitest-code.mdc 独有的接口
    """
    # 转换为集合进行差异计算，去除重复
    final_set = {interface[0] for interface in final_interfaces}
    
    # 获取 code 文件中的接口，去重
    code_dict = {}
    for key, line, content, name in code_interfaces:
        if key not in code_dict:
            code_dict[key] = (line, content, name)
    
    code_set = set(code_dict.keys())
    
    only_in_code_keys = code_set - final_set
    
    # 获取详细信息
    only_in_code = [(key, code_dict[key][0], code_dict[key][1], code_dict[key][2]) for key in only_in_code_keys]
    
    return sorted(only_in_code)

def generate_request_example(method: str, path: str, interface_name: str) -> str:
    """
    根据接口信息生成请求参数示例
    """
    if method == 'GET':
        if '{id}' in path:
            return '''**请求参数示例：**
```json
{
    "id": "123456",
    "include_details": true,
    "format": "json"
}
```'''
        else:
            return '''**请求参数示例：**
```json
{
    "page": 1,
    "per_page": 20,
    "search": "关键词",
    "filters": {
        "status": "active",
        "category": "default"
    }
}
```'''
    
    elif method == 'POST':
        if 'upload' in path.lower() or 'file' in path.lower():
            return '''**请求参数示例：**
```json
{
    "file": "base64_encoded_file_content",
    "filename": "example.pdf",
    "file_type": "application/pdf",
    "description": "文件描述",
    "tags": ["tag1", "tag2"]
}
```'''
        elif 'export' in path.lower():
            return '''**请求参数示例：**
```json
{
    "export_type": "excel",
    "date_range": {
        "start_date": "2024-01-01",
        "end_date": "2024-01-31"
    },
    "filters": {
        "status": "completed",
        "category": "all"
    },
    "format": "xlsx"
}
```'''
        else:
            return '''**请求参数示例：**
```json
{
    "name": "示例名称",
    "description": "详细描述",
    "status": "active",
    "metadata": {
        "created_by": "user123",
        "tags": ["tag1", "tag2"]
    }
}
```'''
    
    elif method == 'PUT':
        return '''**请求参数示例：**
```json
{
    "id": "123456",
    "name": "更新后的名称",
    "description": "更新后的描述",
    "status": "updated",
    "update_fields": ["name", "description", "status"]
}
```'''
    
    elif method == 'DELETE':
        if '{id}' in path:
            return '''**请求参数示例：**
```json
{
    "id": "123456",
    "force_delete": false,
    "reason": "删除原因"
}
```'''
        else:
            return '''**请求参数示例：**
```json
{
    "criteria": {
        "status": "inactive",
        "created_before": "2024-01-01"
    },
    "confirm": true,
    "batch_size": 100
}
```'''
    
    return '''**请求参数示例：**
```json
{
    "data": "示例数据"
}
```'''

def generate_success_response(method: str, path: str, interface_name: str) -> str:
    """
    生成成功响应示例
    """
    if method == 'GET':
        if 'list' in path.lower() or 'search' in interface_name.lower():
            return '''**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "数据获取成功",
    "data": {
        "items": [
            {
                "id": "123456",
                "name": "示例项目",
                "status": "active",
                "created_at": "2024-01-01 12:00:00",
                "updated_at": "2024-01-01 12:00:00"
            }
        ],
        "pagination": {
            "current_page": 1,
            "per_page": 20,
            "total": 100,
            "total_pages": 5
        }
    },
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```'''
        else:
            return '''**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "数据获取成功",
    "data": {
        "id": "123456",
        "name": "示例项目",
        "status": "active",
        "details": {
            "description": "详细描述",
            "metadata": {}
        },
        "created_at": "2024-01-01 12:00:00",
        "updated_at": "2024-01-01 12:00:00"
    },
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```'''
    
    elif method == 'POST':
        return '''**成功响应示例 (201)：**
```json
{
    "code": 201,
    "message": "创建成功",
    "data": {
        "id": "123456",
        "name": "新创建的项目",
        "status": "created",
        "created_at": "2024-01-01 12:00:00"
    },
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```'''
    
    elif method == 'PUT':
        return '''**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "更新成功",
    "data": {
        "id": "123456",
        "name": "更新后的项目",
        "status": "updated",
        "updated_at": "2024-01-01 12:00:00"
    },
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```'''
    
    elif method == 'DELETE':
        return '''**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "删除成功",
    "data": {
        "deleted_id": "123456",
        "deleted_at": "2024-01-01 12:00:00",
        "affected_count": 1
    },
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```'''
    
    return '''**成功响应示例 (200)：**
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {},
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```'''

def generate_error_responses(method: str, path: str, interface_name: str) -> str:
    """
    生成错误响应示例
    """
    error_responses = []

    # 通用错误响应
    error_responses.append('''**错误响应示例 (422 - 参数验证失败)：**
```json
{
    "code": 422,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "field_name": ["字段验证失败"],
            "required_field": ["必填字段不能为空"]
        }
    },
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```''')

    # 根据方法添加特定错误
    if '{id}' in path:
        error_responses.append('''**错误响应示例 (404 - 资源不存在)：**
```json
{
    "code": 404,
    "message": "资源不存在",
    "data": {
        "resource_id": "123456",
        "resource_type": "项目"
    },
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```''')

    if method in ['POST', 'PUT', 'DELETE']:
        error_responses.append('''**错误响应示例 (403 - 权限不足)：**
```json
{
    "code": 403,
    "message": "权限不足",
    "data": {
        "required_permission": "write",
        "current_permission": "read"
    },
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```''')

    # 添加业务特定错误
    if 'export' in path.lower():
        error_responses.append('''**错误响应示例 (1001 - 导出任务创建失败)：**
```json
{
    "code": 1001,
    "message": "导出任务创建失败",
    "data": {
        "reason": "数据量过大",
        "max_records": 10000,
        "current_records": 15000
    },
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```''')

    if 'upload' in path.lower() or 'file' in path.lower():
        error_responses.append('''**错误响应示例 (1002 - 文件上传失败)：**
```json
{
    "code": 1002,
    "message": "文件上传失败",
    "data": {
        "reason": "文件格式不支持",
        "supported_formats": ["pdf", "doc", "docx"],
        "uploaded_format": "txt"
    },
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```''')

    # 通用认证错误
    error_responses.append('''**错误响应示例 (401 - 未登录)：**
```json
{
    "code": 401,
    "message": "请登录后操作",
    "data": [],
    "timestamp": 1640995200,
    "request_id": "req_abc123_def456"
}
```''')

    return '\n\n'.join(error_responses)

def generate_interface_content(interface_key: str, interface_name: str, step_number: int) -> str:
    """
    生成完整的接口内容
    """
    method, path = interface_key.split(' ', 1)

    content = []
    content.append(f"#### 步骤{step_number}: {step_number}.1 {interface_name} {interface_key}")
    content.append("")
    content.append(generate_request_example(method, path, interface_name))
    content.append("")
    content.append(generate_success_response(method, path, interface_name))
    content.append("")
    content.append(generate_error_responses(method, path, interface_name))
    content.append("")

    return '\n'.join(content)

def add_interfaces_to_final_file(final_file_path: str, unique_interfaces: List[Tuple[str, int, str, str]]):
    """
    将独有接口添加到 apitest-final.mdc 文件中
    """
    try:
        # 读取原文件内容
        with open(final_file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 准备要添加的内容
        new_content = []
        new_content.append("\n")
        new_content.append("---")
        new_content.append("")
        new_content.append("## 🟢 **从 apitest-code.mdc 同步的新增接口** (50个接口)")
        new_content.append("")
        new_content.append("### 📋 **测试目标**")
        new_content.append("补充 apitest-code.mdc 中独有的接口功能，完善API文档的完整性。")
        new_content.append("")

        # 生成接口内容
        for i, (interface_key, line_num, original_content, interface_name) in enumerate(unique_interfaces, 1):
            interface_content = generate_interface_content(interface_key, interface_name, i)
            new_content.append(interface_content)

        # 将新内容添加到文件末尾
        final_content = content + '\n'.join(new_content)

        # 写回文件
        with open(final_file_path, 'w', encoding='utf-8') as f:
            f.write(final_content)

        print(f"✅ 成功添加 {len(unique_interfaces)} 个格式化接口到 {final_file_path}")

    except Exception as e:
        print(f"❌ 添加接口时发生错误: {e}")

def main():
    """主函数"""
    final_file = "apitest-final.mdc"
    code_file = "apitest-code.mdc"

    print("🔧 开始添加 apitest-code.mdc 独有接口...")
    print(f"📄 源文件: {code_file}")
    print(f"📄 目标文件: {final_file}")
    print()

    # 提取两个文件中的API接口
    final_interfaces = extract_api_interfaces_from_final_file(final_file)
    code_interfaces = extract_api_interfaces_from_code_file(code_file)

    if not final_interfaces:
        print("❌ 未找到目标文件中的API接口定义")
        return

    if not code_interfaces:
        print("❌ 未找到源文件中的API接口定义")
        return

    # 找出 code 文件独有接口
    unique_interfaces = find_code_unique_interfaces(final_interfaces, code_interfaces)

    print(f"📊 找到 {len(unique_interfaces)} 个独有接口需要添加")

    if not unique_interfaces:
        print("✅ 没有需要添加的接口")
        return

    print("📋 独有接口列表:")
    for i, (interface_key, line_num, content, name) in enumerate(unique_interfaces, 1):
        print(f"   {i}. {name} - {interface_key}")

    # 添加接口到目标文件
    add_interfaces_to_final_file(final_file, unique_interfaces)

    print()
    print("=" * 70)
    print("🎉 接口添加完成!")
    print(f"✅ 成功添加 {len(unique_interfaces)} 个完整格式的接口")
    print("📋 包含完整的请求参数、成功响应和错误响应")
    print("📁 按照 apitest-final.mdc 的标准格式")
    print("=" * 70)

if __name__ == "__main__":
    main()
